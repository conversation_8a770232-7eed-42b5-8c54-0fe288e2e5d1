import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MetaService } from '../../shared/services/meta.service';

interface TeamMember {
  name: string;
  role: string;
  bio: string;
  image: string;
}

interface ProcessStep {
  title: string;
  description: string;
}

interface ImpactStat {
  number: string;
  label: string;
  description: string;
}

@Component({
  selector: 'app-about',
  imports: [CommonModule, RouterModule],
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss'
})
export class AboutComponent implements OnInit {
  teamMembers: TeamMember[] = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      bio: 'Former fashion industry executive with 15 years of experience in sustainable manufacturing.',
      image: 'assets/images/team-alex.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Head of Design',
      bio: 'Minimalist design expert who believes that great design is invisible until you need it.',
      image: 'assets/images/team-sarah.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Sustainability Director',
      bio: 'Environmental scientist dedicated to making fashion more sustainable, one t-shirt at a time.',
      image: 'assets/images/team-marcus.jpg'
    },
    {
      name: '<PERSON>',
      role: 'Head of Operations',
      bio: 'Operations specialist ensuring every ROOT product meets our high standards for quality and ethics.',
      image: 'assets/images/team-emma.jpg'
    }
  ];

  processSteps: ProcessStep[] = [
    {
      title: 'Sustainable Sourcing',
      description: 'We partner with certified organic cotton farms that use regenerative farming practices to improve soil health and biodiversity.'
    },
    {
      title: 'Ethical Manufacturing',
      description: 'Our manufacturing partners are carefully selected based on their commitment to fair wages, safe working conditions, and environmental responsibility.'
    },
    {
      title: 'Quality Control',
      description: 'Every garment undergoes rigorous testing for durability, comfort, and fit before it earns the ROOT label.'
    },
    {
      title: 'Minimal Packaging',
      description: 'We use 100% recyclable packaging made from post-consumer materials, with no unnecessary plastic or waste.'
    },
    {
      title: 'Carbon Neutral Shipping',
      description: 'All ROOT shipments are carbon neutral, and we partner with logistics companies that share our environmental values.'
    }
  ];

  impactStats: ImpactStat[] = [
    {
      number: '50,000+',
      label: 'Happy Customers',
      description: 'Men who trust ROOT for their everyday essentials'
    },
    {
      number: '100%',
      label: 'Organic Cotton',
      description: 'All our products use certified organic cotton'
    },
    {
      number: '500 tons',
      label: 'CO2 Offset',
      description: 'Carbon emissions offset through our sustainability program'
    },
    {
      number: '15',
      label: 'Partner Farms',
      description: 'Sustainable farms we work with globally'
    }
  ];

  constructor(private metaService: MetaService) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'About Us',
      description: 'Learn about ROOT\'s mission to create premium, sustainable men\'s t-shirts. Discover our story, values, and commitment to ethical fashion.',
      keywords: 'about ROOT, sustainable fashion, ethical clothing, organic cotton, minimalist design',
      url: window.location.href
    });
  }
}
