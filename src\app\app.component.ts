import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { RouterOutlet, ChildrenOutletContexts } from '@angular/router';
import { NavigationComponent } from './shared/components/navigation/navigation.component';
import { FooterComponent } from './shared/components/footer/footer.component';
import { CursorService } from './shared/services/cursor.service';
import { slideInAnimation } from './shared/animations/route-animations';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, NavigationComponent, FooterComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  animations: [slideInAnimation]
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'ROOT';

  constructor(
    private cursorService: CursorService,
    private contexts: ChildrenOutletContexts
  ) {}

  ngOnInit() {
    // Initialize custom cursor on desktop
    if (window.innerWidth > 768) {
      setTimeout(() => {
        this.cursorService.init();
      }, 1000);
    }
  }

  ngOnDestroy() {
    this.cursorService.destroy();
  }

  getRouteAnimationData() {
    return this.contexts.getContext('primary')?.route?.snapshot?.data?.['animation'];
  }
}
