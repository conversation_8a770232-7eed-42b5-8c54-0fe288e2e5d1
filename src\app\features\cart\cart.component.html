<!-- <PERSON><PERSON> -->
<section class="cart-header">
  <div class="container">
    <h1 class="page-title">Shopping Cart</h1>
    <p class="cart-count" *ngIf="cartItems.length > 0">{{ cartItems.length }} item{{ cartItems.length > 1 ? 's' : '' }} in your cart</p>
  </div>
</section>

<!-- Cart Content -->
<section class="cart-content">
  <div class="container">
    <!-- Empty Cart -->
    <div class="empty-cart" *ngIf="cartItems.length === 0">
      <div class="empty-cart-icon">🛒</div>
      <h2>Your cart is empty</h2>
      <p>Looks like you haven't added any items to your cart yet.</p>
      <a routerLink="/shop" class="btn btn-primary">Start Shopping</a>
    </div>

    <!-- Cart Items -->
    <div class="cart-layout" *ngIf="cartItems.length > 0">
      <div class="cart-items">
        <div class="cart-item" *ngFor="let item of cartItems; trackBy: trackByItem">
          <div class="item-image">
            <img [src]="item.product.image" [alt]="item.product.name" loading="lazy">
          </div>

          <div class="item-details">
            <h3 class="item-name">{{ item.product.name }}</h3>
            <div class="item-options">
              <span class="item-size">Size: {{ item.size }}</span>
              <span class="item-color">Color: {{ item.color }}</span>
            </div>
            <div class="item-price">${{ item.product.price }}</div>
          </div>

          <div class="item-quantity">
            <label for="quantity-{{ item.id }}" class="sr-only">Quantity</label>
            <div class="quantity-controls">
              <button class="quantity-btn" (click)="decreaseQuantity(item)" [disabled]="item.quantity <= 1">-</button>
              <input
                type="number"
                [id]="'quantity-' + item.id"
                [(ngModel)]="item.quantity"
                (change)="updateQuantity(item)"
                min="1"
                max="10"
                class="quantity-input">
              <button class="quantity-btn" (click)="increaseQuantity(item)" [disabled]="item.quantity >= 10">+</button>
            </div>
          </div>

          <div class="item-total">
            ${{ (item.product.price * item.quantity).toFixed(2) }}
          </div>

          <div class="item-actions">
            <button class="remove-btn" (click)="removeItem(item)" aria-label="Remove item">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Cart Summary -->
      <div class="cart-summary">
        <div class="summary-card">
          <h3>Order Summary</h3>

          <div class="summary-line">
            <span>Subtotal</span>
            <span>${{ subtotal.toFixed(2) }}</span>
          </div>

          <div class="summary-line">
            <span>Shipping</span>
            <span *ngIf="subtotal >= freeShippingThreshold; else shippingCost">Free</span>
            <ng-template #shippingCost>
              <span>${{ shipping.toFixed(2) }}</span>
            </ng-template>
          </div>

          <div class="summary-line">
            <span>Tax</span>
            <span>${{ tax.toFixed(2) }}</span>
          </div>

          <div class="summary-line total">
            <span>Total</span>
            <span>${{ total.toFixed(2) }}</span>
          </div>

          <div class="free-shipping-notice" *ngIf="subtotal < freeShippingThreshold">
            <p>Add ${{ (freeShippingThreshold - subtotal).toFixed(2) }} more for free shipping!</p>
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="(subtotal / freeShippingThreshold) * 100"></div>
            </div>
          </div>

          <div class="checkout-actions">
            <button class="btn btn-primary checkout-btn" (click)="proceedToCheckout()">
              Proceed to Checkout
            </button>
            <a routerLink="/shop" class="btn btn-secondary">Continue Shopping</a>
          </div>

          <div class="payment-methods">
            <p>We accept:</p>
            <div class="payment-icons">
              <span class="payment-icon">💳</span>
              <span class="payment-icon">🏦</span>
              <span class="payment-icon">📱</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Recommended Products -->
<section class="recommended-section" *ngIf="cartItems.length > 0">
  <div class="container">
    <h2 class="section-title">You might also like</h2>
    <div class="recommended-grid">
      <div class="product-card" *ngFor="let product of recommendedProducts">
        <div class="product-image">
          <img [src]="product.image" [alt]="product.name" loading="lazy">
          <div class="product-overlay">
            <button class="quick-add-btn" (click)="quickAdd(product)">Quick Add</button>
          </div>
        </div>
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-price">${{ product.price }}</div>
        </div>
      </div>
    </div>
  </div>
</section>
