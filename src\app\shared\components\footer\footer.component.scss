.footer {
  background: #000;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
}

.footer-section {
  h3, h4, h5 {
    margin-bottom: 1rem;
    color: white;
  }

  h3 {
    font-size: 1.75rem;
    font-weight: 800;
  }

  h4 {
    font-size: 1.1rem;
    font-weight: 600;
  }

  h5 {
    font-size: 1rem;
    font-weight: 600;
  }

  p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: 0.75rem;

    a {
      color: #ccc;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: white;
      }
    }
  }
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;

  a {
    color: #ccc;
    transition: color 0.3s ease;

    &:hover {
      color: white;
    }

    svg {
      width: 24px;
      height: 24px;
    }
  }
}

.footer-newsletter {
  display: flex;
  gap: 0;
  margin-top: 0.5rem;
}

.footer-email-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #333;
  background: #111;
  color: white;
  border-radius: 0;
  outline: none;

  &::placeholder {
    color: #666;
  }

  &:focus {
    border-color: #555;
  }
}

.footer-subscribe-btn {
  padding: 0.75rem 1rem;
  background: #333;
  color: white;
  border: 1px solid #333;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #555;
    border-color: #555;
  }
}

.footer-bottom {
  border-top: 1px solid #333;
  margin-top: 2rem;
  padding-top: 1.5rem;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-methods {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ccc;

  span {
    font-size: 0.9rem;
  }
}

.payment-icons {
  display: flex;
  gap: 0.5rem;

  .payment-icon {
    font-size: 1.25rem;
  }
}

.copyright {
  p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 0 1rem;
  }

  .payment-methods {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .footer-newsletter {
    flex-direction: column;
  }

  .footer-email-input,
  .footer-subscribe-btn {
    border-radius: 0;
  }
}
