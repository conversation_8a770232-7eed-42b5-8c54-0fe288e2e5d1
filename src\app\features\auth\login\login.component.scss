// Auth Section
.auth-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 2rem 1rem;
}

.auth-container {
  width: 100%;
  max-width: 450px;
}

.auth-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.auth-header {
  text-align: center;
  margin-bottom: 2.5rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
  }
}

// Form Styles
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 600;
    color: #000;
    font-size: 0.95rem;
  }
}

.form-input {
  padding: 1rem 1.25rem;
  border: 2px solid #eee;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
  background: #f8f9fa;

  &:focus {
    border-color: #000;
    background: white;
    transform: translateY(-1px);
  }

  &.error {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
  }

  &::placeholder {
    color: #999;
  }
}

.password-input {
  position: relative;

  .form-input {
    padding-right: 3.5rem;
  }

  .password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;

    &:hover {
      color: #000;
    }
  }
}

.form-error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;

  span {
    display: block;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.95rem;
  color: #666;

  input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 2px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  input:checked + .checkmark {
    background: #000;
    border-color: #000;

    &::after {
      opacity: 1;
    }
  }

  a {
    color: #000;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.forgot-password {
  color: #666;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;

  &:hover {
    color: #000;
    text-decoration: underline;
  }
}

// Button Styles
.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  &.btn-primary {
    background: #000;
    color: white;

    &:hover:not(:disabled) {
      background: #333;
      transform: translateY(-2px);
    }
  }

  &.btn-social {
    background: white;
    color: #333;
    border: 2px solid #eee;

    &:hover:not(:disabled) {
      border-color: #ddd;
      transform: translateY(-1px);
    }

    &.google:hover {
      border-color: #4285F4;
    }

    &.facebook:hover {
      border-color: #1877F2;
    }
  }
}

.loading-spinner {
  svg {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Divider
.auth-divider {
  position: relative;
  text-align: center;
  margin: 2rem 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #eee;
  }

  span {
    background: white;
    padding: 0 1rem;
    color: #999;
    font-size: 0.9rem;
  }
}

// Social Login
.social-login {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Footer
.auth-footer {
  text-align: center;
  margin-top: 2rem;

  p {
    color: #666;
    margin: 0;

    a {
      color: #000;
      text-decoration: none;
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .auth-section {
    padding: 1rem;
  }

  .auth-card {
    padding: 2rem;
    border-radius: 15px;
  }

  .auth-header h1 {
    font-size: 2rem;
  }

  .form-input {
    padding: 0.875rem 1rem;
  }

  .btn {
    padding: 0.875rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 1.5rem;
  }

  .auth-header h1 {
    font-size: 1.75rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
