import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MetaService } from '../../shared/services/meta.service';
import { CartService } from '../../shared/services/cart.service';
import { WishlistService } from '../../shared/services/wishlist.service';

interface Category {
  name: string;
  slug: string;
  image: string;
  count: number;
}

interface Color {
  name: string;
  value: string;
  hex: string;
}

interface Product {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  colors: Color[];
  sizes: string[];
  rating: number;
  reviewCount: number;
  isNew: boolean;
  onSale: boolean;
  category: string;
}

@Component({
  selector: 'app-catalog',
  imports: [CommonModule, FormsModule],
  templateUrl: './catalog.component.html',
  styleUrl: './catalog.component.scss'
})
export class CatalogComponent implements OnInit {
  // Filter properties
  searchTerm = '';
  selectedCategory = '';
  selectedSort = 'featured';
  showFilters = false;
  viewMode: 'grid' | 'list' = 'grid';
  selectedSizes: string[] = [];
  selectedColors: string[] = [];
  priceRange = { min: 0, max: 100 };

  // Loading states
  isLoading = false;
  hasMoreProducts = true;

  // Data
  categories: Category[] = [
    { name: 'Casual', slug: 'casual', image: 'assets/images/category-casual.jpg', count: 24 },
    { name: 'Urban', slug: 'urban', image: 'assets/images/category-urban.jpg', count: 18 },
    { name: 'Sporty', slug: 'sporty', image: 'assets/images/category-sporty.jpg', count: 15 },
    { name: 'Oversized', slug: 'oversized', image: 'assets/images/category-oversized.jpg', count: 12 },
    { name: 'Premium', slug: 'premium', image: 'assets/images/category-premium.jpg', count: 8 }
  ];

  sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];

  colors: Color[] = [
    { name: 'Black', value: 'black', hex: '#000000' },
    { name: 'White', value: 'white', hex: '#FFFFFF' },
    { name: 'Gray', value: 'gray', hex: '#808080' },
    { name: 'Navy', value: 'navy', hex: '#000080' },
    { name: 'Olive', value: 'olive', hex: '#808000' },
    { name: 'Burgundy', value: 'burgundy', hex: '#800020' }
  ];

  allProducts: Product[] = [
    {
      id: 1, name: 'Essential White Tee', price: 29.99, image: 'assets/images/product-1.jpg',
      colors: [{ name: 'White', value: 'white', hex: '#FFFFFF' }], sizes: ['S', 'M', 'L', 'XL'],
      rating: 5, reviewCount: 124, isNew: false, onSale: false, category: 'casual'
    },
    {
      id: 2, name: 'Classic Black Tee', price: 29.99, image: 'assets/images/product-2.jpg',
      colors: [{ name: 'Black', value: 'black', hex: '#000000' }], sizes: ['XS', 'S', 'M', 'L', 'XL'],
      rating: 5, reviewCount: 98, isNew: false, onSale: false, category: 'casual'
    },
    {
      id: 3, name: 'Vintage Gray Tee', price: 34.99, image: 'assets/images/product-3.jpg',
      colors: [{ name: 'Gray', value: 'gray', hex: '#808080' }], sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      rating: 4, reviewCount: 76, isNew: true, onSale: false, category: 'urban'
    },
    {
      id: 4, name: 'Premium Navy Tee', price: 39.99, originalPrice: 49.99, image: 'assets/images/product-4.jpg',
      colors: [{ name: 'Navy', value: 'navy', hex: '#000080' }], sizes: ['M', 'L', 'XL'],
      rating: 5, reviewCount: 156, isNew: false, onSale: true, category: 'premium'
    },
    {
      id: 5, name: 'Oversized Cream Tee', price: 32.99, image: 'assets/images/product-5.jpg',
      colors: [{ name: 'Cream', value: 'cream', hex: '#F5F5DC' }], sizes: ['S', 'M', 'L', 'XL'],
      rating: 4, reviewCount: 89, isNew: true, onSale: false, category: 'oversized'
    },
    {
      id: 6, name: 'Minimalist Olive Tee', price: 31.99, image: 'assets/images/product-6.jpg',
      colors: [{ name: 'Olive', value: 'olive', hex: '#808000' }], sizes: ['XS', 'S', 'M', 'L', 'XL'],
      rating: 5, reviewCount: 67, isNew: false, onSale: false, category: 'casual'
    },
    {
      id: 7, name: 'Urban Charcoal Tee', price: 33.99, image: 'assets/images/product-7.jpg',
      colors: [{ name: 'Charcoal', value: 'charcoal', hex: '#36454F' }], sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      rating: 4, reviewCount: 112, isNew: false, onSale: false, category: 'urban'
    },
    {
      id: 8, name: 'Classic Burgundy Tee', price: 29.99, image: 'assets/images/product-8.jpg',
      colors: [{ name: 'Burgundy', value: 'burgundy', hex: '#800020' }], sizes: ['S', 'M', 'L', 'XL'],
      rating: 5, reviewCount: 143, isNew: false, onSale: false, category: 'casual'
    }
  ];

  filteredProducts: Product[] = [];

  constructor(
    private metaService: MetaService,
    private router: Router,
    private cartService: CartService,
    private wishlistService: WishlistService
  ) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'Shop Collection',
      description: 'Browse our complete collection of premium men\'s t-shirts. Find your perfect fit from casual to premium styles.',
      keywords: 'shop t-shirts, men\'s clothing, premium tees, casual wear, urban style',
      url: window.location.href
    });

    this.filteredProducts = [...this.allProducts];
  }

  // Filter methods
  onSearch() {
    this.applyFilters();
  }

  onCategoryChange() {
    this.applyFilters();
  }

  onSortChange() {
    this.applyFilters();
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  onSizeChange(event: any) {
    const size = event.target.value;
    if (event.target.checked) {
      this.selectedSizes.push(size);
    } else {
      this.selectedSizes = this.selectedSizes.filter(s => s !== size);
    }
    this.applyFilters();
  }

  onColorChange(event: any) {
    const color = event.target.value;
    if (event.target.checked) {
      this.selectedColors.push(color);
    } else {
      this.selectedColors = this.selectedColors.filter(c => c !== color);
    }
    this.applyFilters();
  }

  onPriceChange() {
    this.applyFilters();
  }

  setViewMode(mode: 'grid' | 'list') {
    this.viewMode = mode;
  }

  trackByProduct(index: number, product: Product): number {
    return product.id;
  }

  private applyFilters() {
    let filtered = [...this.allProducts];

    // Search filter
    if (this.searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (this.selectedCategory) {
      filtered = filtered.filter(product => product.category === this.selectedCategory);
    }

    // Size filter
    if (this.selectedSizes.length > 0) {
      filtered = filtered.filter(product =>
        product.sizes.some(size => this.selectedSizes.includes(size))
      );
    }

    // Color filter
    if (this.selectedColors.length > 0) {
      filtered = filtered.filter(product =>
        product.colors.some(color => this.selectedColors.includes(color.value))
      );
    }

    // Price filter
    filtered = filtered.filter(product =>
      product.price >= this.priceRange.min && product.price <= this.priceRange.max
    );

    // Sort
    switch (this.selectedSort) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'newest':
        filtered.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0));
        break;
      default:
        // Featured - keep original order
        break;
    }

    this.filteredProducts = filtered;
  }

  // Product actions
  viewProduct(product: Product) {
    this.router.navigate(['/product', product.id]);
  }

  quickView(product: Product) {
    console.log('Quick view:', product);
    // Implement quick view modal
  }

  addToCart(product: Product) {
    const cartItem = {
      productId: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      size: product.sizes[0], // Default to first available size
      color: product.colors[0].name, // Default to first available color
      quantity: 1
    };

    this.cartService.addToCart(cartItem);
    console.log('Added to cart:', cartItem);
  }

  loadMore() {
    this.isLoading = true;
    // Simulate loading more products
    setTimeout(() => {
      this.isLoading = false;
      this.hasMoreProducts = false; // For demo purposes
    }, 1000);
  }

  toggleWishlist(product: Product) {
    const wishlistItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.image,
      colors: product.colors,
      sizes: product.sizes,
      rating: product.rating,
      reviewCount: product.reviewCount,
      isNew: product.isNew,
      onSale: product.onSale,
      category: product.category
    };

    this.wishlistService.toggleWishlist(wishlistItem);
    console.log('Wishlist toggled for:', product.name);
  }

  isInWishlist(productId: number): boolean {
    return this.wishlistService.isInWishlist(productId);
  }
}
