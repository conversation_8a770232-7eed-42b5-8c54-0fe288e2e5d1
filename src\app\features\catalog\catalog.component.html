<!-- Shop Header -->
<section class="shop-header">
  <div class="container">
    <h1 class="page-title">Shop Collection</h1>
    <p class="page-subtitle">Discover our premium men's t-shirts crafted for the modern minimalist</p>
  </div>
</section>

<!-- Filters & Search -->
<section class="filters-section">
  <div class="container">
    <div class="filters-bar">
      <div class="search-box">
        <input type="text" placeholder="Search products..." [(ngModel)]="searchTerm" (input)="onSearch()">
        <button class="search-btn" aria-label="Search">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
        </button>
      </div>

      <div class="filter-controls">
        <select [(ngModel)]="selectedCategory" (change)="onCategoryChange()" class="filter-select">
          <option value="">All Categories</option>
          <option *ngFor="let category of categories" [value]="category.slug">{{ category.name }}</option>
        </select>

        <select [(ngModel)]="selectedSort" (change)="onSortChange()" class="filter-select">
          <option value="featured">Featured</option>
          <option value="price-low">Price: Low to High</option>
          <option value="price-high">Price: High to Low</option>
          <option value="newest">Newest</option>
        </select>

        <button class="filter-toggle" (click)="toggleFilters()" [class.active]="showFilters">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
          Filters
        </button>
      </div>
    </div>

    <!-- Advanced Filters -->
    <div class="advanced-filters" [class.show]="showFilters">
      <div class="filter-group">
        <h4>Size</h4>
        <div class="size-options">
          <label *ngFor="let size of sizes" class="size-option">
            <input type="checkbox" [value]="size" (change)="onSizeChange($event)">
            <span>{{ size }}</span>
          </label>
        </div>
      </div>

      <div class="filter-group">
        <h4>Color</h4>
        <div class="color-options">
          <label *ngFor="let color of colors" class="color-option">
            <input type="checkbox" [value]="color.value" (change)="onColorChange($event)">
            <span class="color-swatch" [style.background-color]="color.hex"></span>
            <span>{{ color.name }}</span>
          </label>
        </div>
      </div>

      <div class="filter-group">
        <h4>Price Range</h4>
        <div class="price-range">
          <input type="range" min="0" max="100" [(ngModel)]="priceRange.min" (input)="onPriceChange()">
          <input type="range" min="0" max="100" [(ngModel)]="priceRange.max" (input)="onPriceChange()">
          <div class="price-display">
            ${{ priceRange.min }} - ${{ priceRange.max }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Products Grid -->
<section class="products-section">
  <div class="container">
    <div class="products-header">
      <p class="results-count">{{ filteredProducts.length }} products found</p>
      <div class="view-toggle">
        <button [class.active]="viewMode === 'grid'" (click)="setViewMode('grid')" aria-label="Grid view">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <rect x="3" y="3" width="7" height="7"></rect>
            <rect x="14" y="3" width="7" height="7"></rect>
            <rect x="14" y="14" width="7" height="7"></rect>
            <rect x="3" y="14" width="7" height="7"></rect>
          </svg>
        </button>
        <button [class.active]="viewMode === 'list'" (click)="setViewMode('list')" aria-label="List view">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <line x1="8" y1="6" x2="21" y2="6"></line>
            <line x1="8" y1="12" x2="21" y2="12"></line>
            <line x1="8" y1="18" x2="21" y2="18"></line>
            <line x1="3" y1="6" x2="3.01" y2="6"></line>
            <line x1="3" y1="12" x2="3.01" y2="12"></line>
            <line x1="3" y1="18" x2="3.01" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>

    <div class="products-grid" [class.list-view]="viewMode === 'list'">
      <div class="product-card" *ngFor="let product of filteredProducts; trackBy: trackByProduct">
        <div class="product-image" (click)="viewProduct(product)">
          <img [src]="product.image" [alt]="product.name" loading="lazy">
          <div class="product-badges">
            <span class="badge new" *ngIf="product.isNew">New</span>
            <span class="badge sale" *ngIf="product.onSale">Sale</span>
          </div>
          <div class="product-overlay">
            <button class="quick-view-btn" (click)="quickView(product); $event.stopPropagation()">Quick View</button>
            <button class="add-to-cart-btn" (click)="addToCart(product); $event.stopPropagation()">Add to Cart</button>
          </div>
        </div>
        <div class="product-info" (click)="viewProduct(product)">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-price">
            <span class="current-price">${{ product.price }}</span>
            <span class="original-price" *ngIf="product.originalPrice">${{ product.originalPrice }}</span>
          </div>
          <div class="product-colors">
            <span *ngFor="let color of product.colors"
                  class="color-dot"
                  [style.background-color]="color.hex"
                  [title]="color.name"></span>
          </div>
          <div class="product-rating">
            <div class="stars">
              <span *ngFor="let star of [1,2,3,4,5]"
                    class="star"
                    [class.filled]="star <= product.rating">★</span>
            </div>
            <span class="rating-count">({{ product.reviewCount }})</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Load More -->
    <div class="load-more" *ngIf="hasMoreProducts">
      <button class="btn btn-outline" (click)="loadMore()" [disabled]="isLoading">
        <span *ngIf="!isLoading">Load More Products</span>
        <span *ngIf="isLoading">Loading...</span>
      </button>
    </div>
  </div>
</section>
