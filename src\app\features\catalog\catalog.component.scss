// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

// Shop Header
.shop-header {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #000;
}

.page-subtitle {
  font-size: 1.25rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

// Filters Section
.filters-section {
  padding: 2rem 0;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 80px;
  z-index: 100;
}

.filters-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;

  input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #eee;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;

    &:focus {
      border-color: #000;
    }
  }

  .search-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;

    &:hover {
      color: #000;
    }
  }
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #eee;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  cursor: pointer;
  outline: none;

  &:focus {
    border-color: #000;
  }
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: none;
  border: 2px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover, &.active {
    border-color: #000;
    background: #000;
    color: white;
  }
}

// Advanced Filters
.advanced-filters {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.show {
    max-height: 300px;
    padding-top: 2rem;
    border-top: 1px solid #eee;
  }
}

.filter-group {
  margin-bottom: 2rem;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #000;
  }
}

.size-options {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.size-option {
  display: flex;
  align-items: center;
  cursor: pointer;

  input {
    display: none;
  }

  span {
    padding: 0.5rem 1rem;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  input:checked + span {
    border-color: #000;
    background: #000;
    color: white;
  }
}

.color-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.color-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;

  input {
    display: none;
  }

  .color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #eee;
    transition: all 0.3s ease;
  }

  input:checked + .color-swatch {
    border-color: #000;
    transform: scale(1.2);
  }

  span:last-child {
    font-size: 0.9rem;
    color: #666;
  }
}

.price-range {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  input[type="range"] {
    width: 100%;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    outline: none;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      background: #000;
      border-radius: 50%;
      cursor: pointer;
    }

    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
      background: #000;
      border-radius: 50%;
      cursor: pointer;
      border: none;
    }
  }

  .price-display {
    text-align: center;
    font-weight: 600;
    color: #000;
  }
}

// Products Section
.products-section {
  padding: 3rem 0;
  background: #f8f9fa;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.results-count {
  color: #666;
  font-size: 0.9rem;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;

  button {
    padding: 0.5rem;
    background: white;
    border: 2px solid #eee;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover, &.active {
      border-color: #000;
      background: #000;
      color: white;
    }
  }
}

// Products Grid
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;

  &.list-view {
    grid-template-columns: 1fr;

    .product-card {
      display: flex;
      align-items: center;

      .product-image {
        width: 200px;
        height: 200px;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;
        padding: 1.5rem;
      }
    }
  }
}

.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);

    .product-overlay {
      opacity: 1;
      visibility: visible;
    }

    .product-image img {
      transform: scale(1.05);
    }
  }
}

.product-image {
  position: relative;
  height: 300px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.product-badges {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  gap: 0.5rem;

  .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;

    &.new {
      background: #28a745;
      color: white;
    }

    &.sale {
      background: #dc3545;
      color: white;
    }
  }
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &.quick-view-btn {
      background: white;
      color: #000;

      &:hover {
        background: #f8f9fa;
        transform: scale(1.05);
      }
    }

    &.add-to-cart-btn {
      background: #000;
      color: white;

      &:hover {
        background: #333;
        transform: scale(1.05);
      }
    }
  }
}

.product-info {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;

  .current-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #000;
  }

  .original-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
  }
}

.product-colors {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;

  .color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #eee;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .stars {
    display: flex;
    gap: 0.125rem;

    .star {
      color: #ddd;
      font-size: 1rem;

      &.filled {
        color: #ffc107;
      }
    }
  }

  .rating-count {
    font-size: 0.875rem;
    color: #666;
  }
}

// Load More
.load-more {
  text-align: center;

  .btn {
    padding: 1rem 2rem;
    background: white;
    color: #000;
    border: 2px solid #000;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: #000;
      color: white;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.wishlist-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: #666;
  z-index: 2;

  &:hover {
    background: white;
    color: #dc3545;
    transform: scale(1.1);
  }

  &.active {
    background: #dc3545;
    color: white;

    &:hover {
      background: #c82333;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .filters-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-controls {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .filters-section {
    position: static;
  }

  .filters-bar {
    gap: 1rem;
  }

  .search-box {
    max-width: none;
  }

  .filter-controls {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;

    &.list-view {
      .product-card {
        flex-direction: column;

        .product-image {
          width: 100%;
          height: 250px;
        }
      }
    }
  }

  .product-image {
    height: 250px;
  }

  .view-toggle {
    display: none;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .advanced-filters {
    &.show {
      max-height: 500px;
    }
  }

  .filter-group {
    margin-bottom: 1.5rem;
  }

  .size-options, .color-options {
    gap: 0.5rem;
  }
}
