import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { gsap } from 'gsap';

@Injectable({
  providedIn: 'root'
})
export class CursorService {
  private renderer: Renderer2;
  private cursor!: HTMLElement;
  private cursorFollower!: HTMLElement;
  private isInitialized = false;

  constructor(private rendererFactory: RendererFactory2) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
  }

  init() {
    if (this.isInitialized || window.innerWidth <= 768) return;

    // Create cursor elements
    this.cursor = this.renderer.createElement('div');
    this.cursorFollower = this.renderer.createElement('div');

    this.renderer.addClass(this.cursor, 'custom-cursor');
    this.renderer.addClass(this.cursorFollower, 'custom-cursor-follower');

    this.renderer.appendChild(document.body, this.cursor);
    this.renderer.appendChild(document.body, this.cursorFollower);

    // Add styles
    this.addCursorStyles();

    // Add event listeners
    this.addEventListeners();

    this.isInitialized = true;
  }

  private addCursorStyles() {
    const style = this.renderer.createElement('style');
    this.renderer.setProperty(style, 'innerHTML', `
      .custom-cursor {
        position: fixed;
        top: 0;
        left: 0;
        width: 8px;
        height: 8px;
        background: #000;
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        mix-blend-mode: difference;
        transition: transform 0.1s ease;
      }

      .custom-cursor-follower {
        position: fixed;
        top: 0;
        left: 0;
        width: 40px;
        height: 40px;
        border: 1px solid rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9998;
        transition: transform 0.3s ease;
      }

      .custom-cursor.hover {
        transform: scale(2);
      }

      .custom-cursor-follower.hover {
        transform: scale(1.5);
        border-color: rgba(0, 0, 0, 0.6);
      }

      body.custom-cursor-active {
        cursor: none;
      }

      body.custom-cursor-active * {
        cursor: none !important;
      }
    `);
    this.renderer.appendChild(document.head, style);
  }

  private addEventListeners() {
    this.renderer.addClass(document.body, 'custom-cursor-active');

    // Mouse move
    this.renderer.listen(document, 'mousemove', (e: MouseEvent) => {
      gsap.to(this.cursor, {
        x: e.clientX - 4,
        y: e.clientY - 4,
        duration: 0.1,
        ease: 'power2.out'
      });

      gsap.to(this.cursorFollower, {
        x: e.clientX - 20,
        y: e.clientY - 20,
        duration: 0.3,
        ease: 'power2.out'
      });
    });

    // Hover effects
    const hoverElements = document.querySelectorAll('a, button, .product-card, .category-card, .instagram-item');
    hoverElements.forEach(element => {
      this.renderer.listen(element, 'mouseenter', () => {
        this.renderer.addClass(this.cursor, 'hover');
        this.renderer.addClass(this.cursorFollower, 'hover');
      });

      this.renderer.listen(element, 'mouseleave', () => {
        this.renderer.removeClass(this.cursor, 'hover');
        this.renderer.removeClass(this.cursorFollower, 'hover');
      });
    });
  }

  destroy() {
    if (!this.isInitialized) return;

    if (this.cursor) {
      this.renderer.removeChild(document.body, this.cursor);
    }
    if (this.cursorFollower) {
      this.renderer.removeChild(document.body, this.cursorFollower);
    }

    this.renderer.removeClass(document.body, 'custom-cursor-active');
    this.isInitialized = false;
  }
}
