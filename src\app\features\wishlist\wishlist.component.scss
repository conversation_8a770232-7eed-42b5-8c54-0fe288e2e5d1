// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

// Breadcrumb
.breadcrumb-section {
  padding: 6rem 0 2rem;
  background: #f8f9fa;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;

  a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: #000;
    }
  }

  .separator {
    color: #ccc;
  }

  .current {
    color: #000;
    font-weight: 500;
  }
}

// Wishlist Section
.wishlist-section {
  padding: 3rem 0;
  background: white;
  min-height: 60vh;
}

.wishlist-header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 0.5rem;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
  }
}

// Empty State
.empty-wishlist {
  text-align: center;
  padding: 4rem 2rem;

  .empty-icon {
    margin-bottom: 2rem;
    color: #ccc;
  }

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #000;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

// Wishlist Grid
.wishlist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.wishlist-item {
  background: #f8f9fa;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }
}

.item-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.item-badges {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  gap: 0.5rem;

  .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;

    &.new {
      background: #28a745;
      color: white;
    }

    &.sale {
      background: #dc3545;
      color: white;
    }
  }
}

.remove-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: #666;

  &:hover {
    background: white;
    color: #dc3545;
    transform: scale(1.1);
  }
}

.item-info {
  padding: 1.5rem;
  cursor: pointer;

  .item-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #000;
  }

  .item-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .current-price {
      font-size: 1.25rem;
      font-weight: 700;
      color: #000;
    }

    .original-price {
      font-size: 1rem;
      color: #999;
      text-decoration: line-through;
    }
  }

  .item-colors {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .color-dot {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
    }
  }

  .item-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .stars {
      display: flex;
      gap: 0.125rem;

      .star {
        color: #ddd;
        font-size: 1rem;

        &.filled {
          color: #ffc107;
        }
      }
    }

    .rating-count {
      color: #666;
      font-size: 0.9rem;
    }
  }
}

.item-actions {
  padding: 0 1.5rem 1.5rem;
  display: flex;
  gap: 1rem;

  .btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;

    &.btn-primary {
      background: #000;
      color: white;

      &:hover {
        background: #333;
        transform: translateY(-1px);
      }
    }

    &.btn-secondary {
      background: transparent;
      color: #666;
      border: 2px solid #eee;

      &:hover {
        color: #dc3545;
        border-color: #dc3545;
      }
    }
  }
}

// Wishlist Actions
.wishlist-actions {
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;

  .btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &.btn-primary {
      background: #000;
      color: white;

      &:hover {
        background: #333;
        transform: translateY(-2px);
      }
    }

    &.btn-secondary {
      background: transparent;
      color: #666;
      border: 2px solid #eee;

      &:hover {
        color: #000;
        border-color: #000;
        transform: translateY(-2px);
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .breadcrumb-section {
    padding: 5rem 0 1rem;
  }

  .wishlist-header h1 {
    font-size: 2rem;
  }

  .wishlist-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .wishlist-actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;

    .btn {
      width: 100%;
      max-width: 300px;
    }
  }
}

@media (max-width: 480px) {
  .wishlist-grid {
    grid-template-columns: 1fr;
  }

  .item-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
}
