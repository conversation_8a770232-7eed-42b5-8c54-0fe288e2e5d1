import { Component, HostListener, OnInit } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-navigation',
  imports: [RouterModule, CommonModule],
  templateUrl: './navigation.component.html',
  styleUrl: './navigation.component.scss'
})
export class NavigationComponent implements OnInit {
  isScrolled = false;
  isMenuOpen = false;
  isUserMenuOpen = false;
  cartItemCount = 2; // This would typically come from a cart service

  // Authentication state
  isLoggedIn = false;
  userName = '';
  userEmail = '';

  constructor(private router: Router) {}

  ngOnInit() {
    // Check authentication status
    this.checkAuthStatus();

    // Listen for storage changes (login/logout from other tabs)
    window.addEventListener('storage', () => {
      this.checkAuthStatus();
    });
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll() {
    this.isScrolled = window.pageYOffset > 50;
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
    // Prevent body scroll when menu is open
    if (this.isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }

  closeMenu() {
    this.isMenuOpen = false;
    document.body.style.overflow = 'auto';
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    // Close mobile menu on desktop resize
    if (window.innerWidth > 768 && this.isMenuOpen) {
      this.closeMenu();
    }
    // Close user menu on resize
    if (this.isUserMenuOpen) {
      this.isUserMenuOpen = false;
    }
  }

  // Authentication methods
  private checkAuthStatus() {
    this.isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    if (this.isLoggedIn) {
      this.userEmail = localStorage.getItem('userEmail') || '';
      this.userName = localStorage.getItem('userName') || this.userEmail.split('@')[0];
    }
  }

  toggleUserMenu() {
    this.isUserMenuOpen = !this.isUserMenuOpen;
  }

  logout() {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userName');
    this.isLoggedIn = false;
    this.userName = '';
    this.userEmail = '';
    this.isUserMenuOpen = false;
    this.router.navigate(['/']);
  }

  // Close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.user-menu')) {
      this.isUserMenuOpen = false;
    }
  }
}
