import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MetaService } from '../../shared/services/meta.service';

interface Color {
  name: string;
  value: string;
  hex: string;
}

interface Badge {
  text: string;
  type: string;
}

interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface Specification {
  label: string;
  value: string;
}

interface Review {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  date: string;
  text: string;
}

interface Product {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  description: string;
  fullDescription: string;
  images: string[];
  colors: Color[];
  sizes: string[];
  rating: number;
  reviewCount: number;
  badges?: Badge[];
  features: Feature[];
  specifications: Specification[];
  reviews: Review[];
  category: string;
}

interface Tab {
  id: string;
  label: string;
}

@Component({
  selector: 'app-product-detail',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './product-detail.component.html',
  styleUrl: './product-detail.component.scss'
})
export class ProductDetailComponent implements OnInit {
  product: Product | null = null;
  selectedImage = '';
  selectedSize = '';
  selectedColor: Color | null = null;
  quantity = 1;
  activeTab = 'description';
  isInWishlist = false;

  tabs: Tab[] = [
    { id: 'description', label: 'Description' },
    { id: 'specifications', label: 'Specifications' },
    { id: 'reviews', label: 'Reviews' }
  ];

  relatedProducts: any[] = [
    { id: 2, name: 'Classic Black Tee', price: 29.99, image: 'assets/images/product-2.jpg' },
    { id: 3, name: 'Vintage Gray Tee', price: 34.99, image: 'assets/images/product-3.jpg' },
    { id: 4, name: 'Premium Navy Tee', price: 39.99, image: 'assets/images/product-4.jpg' },
    { id: 5, name: 'Oversized Cream Tee', price: 32.99, image: 'assets/images/product-5.jpg' }
  ];

  constructor(
    private route: ActivatedRoute,
    private metaService: MetaService
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      const productId = +params['id'];
      this.loadProduct(productId);
    });
  }

  private loadProduct(id: number) {
    // Mock product data - in real app, this would come from a service
    this.product = {
      id: id,
      name: 'Essential White Tee',
      price: 29.99,
      originalPrice: 39.99,
      description: 'Our signature essential tee crafted from 100% organic cotton. Perfect for everyday wear with a comfortable fit and timeless design.',
      fullDescription: `
        <p>The Essential White Tee is the cornerstone of any modern wardrobe. Made from premium organic cotton, this tee offers unparalleled comfort and durability.</p>
        <h4>Key Features:</h4>
        <ul>
          <li>100% organic cotton construction</li>
          <li>Pre-shrunk for consistent fit</li>
          <li>Reinforced seams for durability</li>
          <li>Tagless design for comfort</li>
          <li>Machine washable</li>
        </ul>
        <p>Whether you're dressing up or down, this versatile tee adapts to any style. The classic cut ensures a flattering fit for all body types.</p>
      `,
      images: [
        'assets/images/product-1.jpg',
        'assets/images/product-1-alt1.jpg',
        'assets/images/product-1-alt2.jpg',
        'assets/images/product-1-alt3.jpg'
      ],
      colors: [
        { name: 'White', value: 'white', hex: '#FFFFFF' },
        { name: 'Black', value: 'black', hex: '#000000' },
        { name: 'Gray', value: 'gray', hex: '#808080' }
      ],
      sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
      rating: 5,
      reviewCount: 124,
      badges: [
        { text: 'Bestseller', type: 'bestseller' },
        { text: 'Organic', type: 'organic' }
      ],
      features: [
        {
          icon: '🌱',
          title: 'Organic Cotton',
          description: 'Made from 100% certified organic cotton'
        },
        {
          icon: '🚚',
          title: 'Free Shipping',
          description: 'Free shipping on orders over $75'
        },
        {
          icon: '↩️',
          title: 'Easy Returns',
          description: '30-day hassle-free returns'
        },
        {
          icon: '🛡️',
          title: 'Quality Guarantee',
          description: 'Lifetime quality guarantee'
        }
      ],
      specifications: [
        { label: 'Material', value: '100% Organic Cotton' },
        { label: 'Weight', value: '180 GSM' },
        { label: 'Fit', value: 'Regular Fit' },
        { label: 'Care', value: 'Machine Wash Cold' },
        { label: 'Origin', value: 'Made in Portugal' },
        { label: 'Certification', value: 'GOTS Certified' }
      ],
      reviews: [
        {
          id: 1,
          name: 'Alex Johnson',
          avatar: 'assets/images/avatar-1.jpg',
          rating: 5,
          date: '2024-01-15',
          text: 'Perfect fit and amazing quality. This is my third purchase and I\'m never disappointed.'
        },
        {
          id: 2,
          name: 'Marcus Chen',
          avatar: 'assets/images/avatar-2.jpg',
          rating: 5,
          date: '2024-01-10',
          text: 'Love the organic cotton feel. Very comfortable and the white stays bright after multiple washes.'
        },
        {
          id: 3,
          name: 'David Rodriguez',
          avatar: 'assets/images/avatar-3.jpg',
          rating: 4,
          date: '2024-01-05',
          text: 'Great basic tee. Fits true to size and the quality is excellent for the price.'
        }
      ],
      category: 'casual'
    };

    this.selectedImage = this.product.images[0];
    this.selectedColor = this.product.colors[0];

    // Update meta tags
    this.metaService.updatePageMeta({
      title: this.product.name,
      description: this.product.description,
      keywords: `${this.product.name}, men's t-shirt, organic cotton, ROOT brand`,
      url: window.location.href
    });
  }

  // Image selection
  selectImage(image: string) {
    this.selectedImage = image;
  }

  // Size selection
  selectSize(size: string) {
    this.selectedSize = size;
  }

  isSizeAvailable(size: string): boolean {
    // In real app, this would check inventory
    return true;
  }

  // Color selection
  selectColor(color: Color) {
    this.selectedColor = color;
  }

  // Quantity controls
  increaseQuantity() {
    if (this.quantity < 10) {
      this.quantity++;
    }
  }

  decreaseQuantity() {
    if (this.quantity > 1) {
      this.quantity--;
    }
  }

  // Actions
  addToCart() {
    if (!this.selectedSize || !this.selectedColor || !this.product) {
      return;
    }

    const cartItem = {
      product: this.product,
      size: this.selectedSize,
      color: this.selectedColor.name,
      quantity: this.quantity
    };

    console.log('Adding to cart:', cartItem);
    // In real app, this would call a cart service
  }

  toggleWishlist() {
    this.isInWishlist = !this.isInWishlist;
    console.log('Wishlist toggled:', this.isInWishlist);
    // In real app, this would call a wishlist service
  }

  // Tabs
  setActiveTab(tabId: string) {
    this.activeTab = tabId;
  }

  // Utility methods
  getDiscountPercentage(): number {
    if (!this.product?.originalPrice) return 0;
    return Math.round(((this.product.originalPrice - this.product.price) / this.product.originalPrice) * 100);
  }

  quickView(product: any) {
    console.log('Quick view:', product);
    // In real app, this would open a modal or navigate
  }
}
