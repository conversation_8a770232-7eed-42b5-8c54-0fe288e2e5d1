/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: default !important;
}

// Override custom cursor
body.custom-cursor-active,
body.custom-cursor-active * {
  cursor: default !important;
}

// Placeholder images for development
img[src*="assets/images/"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-align: center;
  position: relative;

  &::before {
    content: "Image Placeholder";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.9rem;
    opacity: 0.8;
  }
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;

  &:hover {
    background: #555;
  }
}

// Focus styles
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #000;
  outline-offset: 2px;
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Mobile optimizations
@media (max-width: 768px) {
  // Improve touch targets
  button, .btn, a {
    min-height: 44px;
    min-width: 44px;
  }

  // Better text readability
  body {
    font-size: 16px;
    line-height: 1.6;
  }

  // Prevent zoom on input focus
  input, select, textarea {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  // Smaller containers on very small screens
  .container {
    padding: 0 0.75rem;
  }

  // Improve button spacing
  .btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  // Better form inputs
  input, select, textarea {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }
}
