import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MetaService } from '../../shared/services/meta.service';

interface Product {
  id: number;
  name: string;
  price: number;
  image: string;
}

interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  size: string;
  color: string;
}

@Component({
  selector: 'app-cart',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './cart.component.html',
  styleUrl: './cart.component.scss'
})
export class CartComponent implements OnInit {
  cartItems: CartItem[] = [
    {
      id: '1',
      product: {
        id: 1,
        name: 'Essential White Tee',
        price: 29.99,
        image: 'assets/images/product-1.jpg'
      },
      quantity: 2,
      size: 'M',
      color: 'White'
    },
    {
      id: '2',
      product: {
        id: 2,
        name: 'Classic Black Tee',
        price: 29.99,
        image: 'assets/images/product-2.jpg'
      },
      quantity: 1,
      size: 'L',
      color: 'Black'
    }
  ];

  recommendedProducts: Product[] = [
    { id: 3, name: 'Vintage Gray Tee', price: 34.99, image: 'assets/images/product-3.jpg' },
    { id: 4, name: 'Premium Navy Tee', price: 39.99, image: 'assets/images/product-4.jpg' },
    { id: 5, name: 'Oversized Cream Tee', price: 32.99, image: 'assets/images/product-5.jpg' },
    { id: 6, name: 'Minimalist Olive Tee', price: 31.99, image: 'assets/images/product-6.jpg' }
  ];

  freeShippingThreshold = 75;
  shippingRate = 8.99;
  taxRate = 0.08;

  constructor(private metaService: MetaService) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'Shopping Cart',
      description: 'Review your ROOT t-shirt selections and proceed to checkout. Free shipping on orders over $75.',
      keywords: 'shopping cart, checkout, ROOT t-shirts',
      url: window.location.href
    });
  }

  get subtotal(): number {
    return this.cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  }

  get shipping(): number {
    return this.subtotal >= this.freeShippingThreshold ? 0 : this.shippingRate;
  }

  get tax(): number {
    return this.subtotal * this.taxRate;
  }

  get total(): number {
    return this.subtotal + this.shipping + this.tax;
  }

  trackByItem(index: number, item: CartItem): string {
    return item.id;
  }

  decreaseQuantity(item: CartItem) {
    if (item.quantity > 1) {
      item.quantity--;
    }
  }

  increaseQuantity(item: CartItem) {
    if (item.quantity < 10) {
      item.quantity++;
    }
  }

  updateQuantity(item: CartItem) {
    if (item.quantity < 1) {
      item.quantity = 1;
    } else if (item.quantity > 10) {
      item.quantity = 10;
    }
  }

  removeItem(item: CartItem) {
    const index = this.cartItems.findIndex(cartItem => cartItem.id === item.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }

  proceedToCheckout() {
    console.log('Proceeding to checkout with items:', this.cartItems);
    // Implement checkout logic
  }

  quickAdd(product: Product) {
    console.log('Quick add product:', product);
    // Implement quick add logic
  }
}
