import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MetaService } from '../../shared/services/meta.service';
import { CartService, CartItem } from '../../shared/services/cart.service';
import { Subscription } from 'rxjs';

interface Product {
  id: number;
  name: string;
  price: number;
  image: string;
}

@Component({
  selector: 'app-cart',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './cart.component.html',
  styleUrl: './cart.component.scss'
})
export class CartComponent implements OnInit, OnDestroy {
  cartItems: CartItem[] = [];
  private cartSubscription?: Subscription;

  recommendedProducts: Product[] = [
    { id: 3, name: 'Vintage Gray Tee', price: 34.99, image: 'assets/images/product-3.jpg' },
    { id: 4, name: 'Premium Navy Tee', price: 39.99, image: 'assets/images/product-4.jpg' },
    { id: 5, name: 'Oversized Cream Tee', price: 32.99, image: 'assets/images/product-5.jpg' },
    { id: 6, name: 'Minimalist Olive Tee', price: 31.99, image: 'assets/images/product-6.jpg' }
  ];

  freeShippingThreshold = 75;
  shippingRate = 8.99;
  taxRate = 0.08;

  constructor(
    private metaService: MetaService,
    private cartService: CartService,
    private router: Router
  ) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'Shopping Cart',
      description: 'Review your ROOT t-shirt selections and proceed to checkout. Free shipping on orders over $75.',
      keywords: 'shopping cart, checkout, ROOT t-shirts',
      url: window.location.href
    });

    // Subscribe to cart changes
    this.cartSubscription = this.cartService.cart$.subscribe(items => {
      this.cartItems = items;
    });
  }

  ngOnDestroy() {
    if (this.cartSubscription) {
      this.cartSubscription.unsubscribe();
    }
  }

  get subtotal(): number {
    return this.cartService.getCartTotal();
  }

  get shipping(): number {
    return this.subtotal >= this.freeShippingThreshold ? 0 : this.shippingRate;
  }

  get tax(): number {
    return this.subtotal * this.taxRate;
  }

  get total(): number {
    return this.subtotal + this.shipping + this.tax;
  }

  trackByItem(index: number, item: CartItem): string {
    return item.id;
  }

  decreaseQuantity(item: CartItem) {
    if (item.quantity > 1) {
      this.cartService.updateQuantity(item.id, item.quantity - 1);
    }
  }

  increaseQuantity(item: CartItem) {
    if (item.quantity < 10) {
      this.cartService.updateQuantity(item.id, item.quantity + 1);
    }
  }

  updateQuantity(item: CartItem) {
    const quantity = Math.max(1, Math.min(10, item.quantity));
    this.cartService.updateQuantity(item.id, quantity);
  }

  removeItem(item: CartItem) {
    this.cartService.removeFromCart(item.id);
  }

  proceedToCheckout() {
    if (this.cartItems.length === 0) {
      console.log('Cart is empty');
      return;
    }

    // Navigate to checkout page
    this.router.navigate(['/checkout']);
  }

  quickAdd(product: Product) {
    console.log('Quick add product:', product);
    // Implement quick add logic
  }
}
