import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./features/home/<USER>').then(m => m.HomeComponent),
    data: { animation: 'HomePage' }
  },
  {
    path: 'shop',
    loadComponent: () => import('./features/catalog/catalog.component').then(m => m.CatalogComponent),
    data: { animation: 'ShopPage' }
  },
  {
    path: 'product/:id',
    loadComponent: () => import('./features/product-detail/product-detail.component').then(m => m.ProductDetailComponent),
    data: { animation: 'ProductPage' }
  },
  {
    path: 'about',
    loadComponent: () => import('./features/about/about.component').then(m => m.AboutComponent),
    data: { animation: 'AboutPage' }
  },
  {
    path: 'cart',
    loadComponent: () => import('./features/cart/cart.component').then(m => m.CartComponent),
    data: { animation: 'CartPage' }
  },
  {
    path: 'wishlist',
    loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),
    data: { animation: 'WishlistPage' }
  },
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent),
    data: { animation: 'LoginPage' }
  },
  {
    path: 'signup',
    loadComponent: () => import('./features/auth/signup/signup.component').then(m => m.SignupComponent),
    data: { animation: 'SignupPage' }
  },
  {
    path: '**',
    redirectTo: ''
  }
];
