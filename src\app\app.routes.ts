import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./features/home/<USER>').then(m => m.HomeComponent),
    data: { animation: 'HomePage' }
  },
  {
    path: 'shop',
    loadComponent: () => import('./features/catalog/catalog.component').then(m => m.CatalogComponent),
    data: { animation: 'ShopPage' }
  },
  {
    path: 'about',
    loadComponent: () => import('./features/about/about.component').then(m => m.AboutComponent),
    data: { animation: 'AboutPage' }
  },
  {
    path: 'cart',
    loadComponent: () => import('./features/cart/cart.component').then(m => m.CartComponent),
    data: { animation: 'CartPage' }
  },
  {
    path: '**',
    redirectTo: ''
  }
];
