// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

// Breadcrumb
.breadcrumb-section {
  padding: 6rem 0 2rem;
  background: #f8f9fa;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;

  a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: #000;
    }
  }

  .separator {
    color: #ccc;
  }

  .current {
    color: #000;
    font-weight: 500;
  }
}

// Product Detail
.product-detail-section {
  padding: 3rem 0;
  background: white;
}

.product-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

// Product Images
.product-images {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.main-image {
  position: relative;
  width: 100%;
  height: 600px;
  border-radius: 15px;
  overflow: hidden;
  background: #f8f9fa;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.image-badges {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  gap: 0.5rem;

  .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;

    &.bestseller {
      background: #28a745;
      color: white;
    }

    &.organic {
      background: #17a2b8;
      color: white;
    }

    &.sale {
      background: #dc3545;
      color: white;
    }
  }
}

.thumbnail-images {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: none;

  &.active {
    border-color: #000;
  }

  &:hover {
    border-color: #666;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Product Info
.product-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #000;
  margin: 0;
  line-height: 1.2;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 1rem;

  .stars {
    display: flex;
    gap: 0.125rem;

    .star {
      color: #ddd;
      font-size: 1.25rem;

      &.filled {
        color: #ffc107;
      }
    }
  }

  .rating-text {
    color: #666;
    font-size: 0.9rem;
  }
}

.product-price {
  display: flex;
  align-items: center;
  gap: 1rem;

  .current-price {
    font-size: 2rem;
    font-weight: 700;
    color: #000;
  }

  .original-price {
    font-size: 1.5rem;
    color: #999;
    text-decoration: line-through;
  }

  .discount {
    background: #dc3545;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
  }
}

.product-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666;
}

// Product Options
.product-options {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #000;
}

.size-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.size-option {
  padding: 0.75rem 1rem;
  border: 2px solid #eee;
  background: white;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(.disabled) {
    border-color: #666;
  }

  &.active {
    border-color: #000;
    background: #000;
    color: white;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    text-decoration: line-through;
  }
}

.size-guide {
  color: #666;
  text-decoration: underline;
  font-size: 0.9rem;

  &:hover {
    color: #000;
  }
}

.color-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.color-option {
  width: 40px;
  height: 40px;
  border: 3px solid transparent;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  background: none;

  &:hover {
    transform: scale(1.1);
  }

  &.active {
    border-color: #000;
    transform: scale(1.1);
  }
}

.quantity-selector {
  display: flex;
  align-items: center;
  width: fit-content;
  border: 2px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.quantity-btn {
  width: 50px;
  height: 50px;
  background: white;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: #f8f9fa;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.quantity-input {
  width: 80px;
  height: 50px;
  border: none;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  outline: none;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
}

// Product Actions
.product-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.btn-primary {
    background: #000;
    color: white;

    &:hover:not(:disabled) {
      background: #333;
      transform: translateY(-2px);
    }
  }

  &.btn-secondary {
    background: transparent;
    color: #000;
    border: 2px solid #000;

    &:hover:not(:disabled) {
      background: #000;
      color: white;
      transform: translateY(-2px);
    }
  }
}

.add-to-cart {
  font-size: 1.1rem;
  padding: 1.25rem 2rem;
}

// Product Features
.product-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.feature {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-text {
  h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem;
    color: #000;
  }

  p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
    line-height: 1.4;
  }
}

// Product Tabs
.product-tabs-section {
  padding: 4rem 0;
  background: #f8f9fa;
}

.tabs-header {
  display: flex;
  gap: 0;
  margin-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.tab-btn {
  padding: 1rem 2rem;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #000;
  }

  &.active {
    color: #000;
    font-weight: 600;
    border-bottom-color: #000;
  }
}

.tabs-content {
  background: white;
  border-radius: 15px;
  padding: 2rem;
}

.tab-panel {
  display: none;

  &.active {
    display: block;
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #000;
  }

  p {
    line-height: 1.6;
    color: #666;
    margin-bottom: 1rem;
  }

  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;

    li {
      margin-bottom: 0.5rem;
      color: #666;
      line-height: 1.6;
    }
  }

  h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem;
    color: #000;
  }
}

.specs-table {
  width: 100%;
  border-collapse: collapse;

  tr {
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }

  td {
    padding: 1rem 0;
    vertical-align: top;

    &.spec-label {
      font-weight: 600;
      color: #000;
      width: 30%;
    }

    &.spec-value {
      color: #666;
    }
  }
}

.reviews-summary {
  margin-bottom: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 15px;
  text-align: center;
}

.rating-overview {
  .average-rating {
    font-size: 3rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 0.5rem;
  }

  .stars-large {
    margin-bottom: 1rem;

    .star {
      color: #ddd;
      font-size: 1.5rem;
      margin: 0 0.125rem;

      &.filled {
        color: #ffc107;
      }
    }
  }

  p {
    color: #666;
    margin: 0;
  }
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.review {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 1rem;

  h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.25rem;
    color: #000;
  }
}

.reviewer-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.review-stars {
  display: flex;
  gap: 0.125rem;

  .star {
    color: #ddd;
    font-size: 1rem;

    &.filled {
      color: #ffc107;
    }
  }
}

.review-date {
  color: #999;
  font-size: 0.9rem;
}

.review-text {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

// Related Products
.related-products-section {
  padding: 4rem 0;
  background: white;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  color: #000;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.product-card {
  background: #f8f9fa;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);

    .product-overlay {
      opacity: 1;
      visibility: visible;
    }

    .product-image img {
      transform: scale(1.05);
    }
  }
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.quick-view-btn {
  padding: 0.75rem 1.5rem;
  background: white;
  color: #000;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #f8f9fa;
    transform: scale(1.05);
  }
}

.product-info {
  padding: 1.5rem;
  text-align: center;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #000;
}

// Responsive Design
@media (max-width: 1024px) {
  .product-layout {
    gap: 2rem;
  }

  .main-image {
    height: 500px;
  }

  .product-features {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .breadcrumb-section {
    padding: 5rem 0 1rem;
  }

  .product-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .main-image {
    height: 400px;
  }

  .product-title {
    font-size: 2rem;
  }

  .product-price .current-price {
    font-size: 1.75rem;
  }

  .size-options {
    gap: 0.5rem;
  }

  .size-option {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .product-features {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tabs-header {
    flex-wrap: wrap;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .tabs-content {
    padding: 1.5rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .product-image {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .product-title {
    font-size: 1.75rem;
  }

  .product-actions {
    .btn {
      padding: 1rem;
      font-size: 0.9rem;
    }
  }

  .thumbnail-images {
    gap: 0.5rem;
  }

  .thumbnail {
    width: 60px;
    height: 60px;
  }

  .quantity-selector {
    .quantity-btn {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }

    .quantity-input {
      width: 60px;
      height: 40px;
    }
  }

  .products-grid {
    grid-template-columns: 1fr;
  }
}

// Related Products Wishlist Button
.related-products-section {
  .wishlist-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #666;
    z-index: 2;

    &:hover {
      background: white;
      color: #dc3545;
      transform: scale(1.1);
    }

    &.active {
      background: #dc3545;
      color: white;

      &:hover {
        background: #c82333;
      }
    }
  }

  .product-overlay {
    display: flex;
    gap: 0.5rem;

    .add-to-cart-btn {
      background: #000;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #333;
        transform: translateY(-1px);
      }
    }
  }
}
