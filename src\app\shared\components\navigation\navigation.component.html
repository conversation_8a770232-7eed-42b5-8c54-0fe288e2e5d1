<nav class="navbar" [class.scrolled]="isScrolled">
  <div class="nav-container">
    <!-- Logo -->
    <div class="nav-logo">
      <a routerLink="/" class="logo-link">
        <span class="logo-text">ROOT</span>
      </a>
    </div>

    <!-- Desktop Menu -->
    <div class="nav-menu" [class.active]="isMenuOpen">
      <a routerLink="/" class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a>
      <a routerLink="/shop" class="nav-link" routerLinkActive="active">Shop</a>
      <a routerLink="/about" class="nav-link" routerLinkActive="active">About</a>
      <a href="#" class="nav-link">Blog</a>
      <a href="#" class="nav-link">Contact</a>
    </div>

    <!-- Icons -->
    <div class="nav-icons">
      <button class="icon-btn search-btn" aria-label="Search">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </button>

      <button class="icon-btn cart-btn" aria-label="Cart" routerLink="/cart">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"></path>
          <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"></path>
          <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"></path>
        </svg>
        <span class="cart-count" *ngIf="cartItemCount > 0">{{ cartItemCount }}</span>
      </button>

      <button class="icon-btn profile-btn" aria-label="Profile">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      </button>
    </div>

    <!-- Mobile Hamburger -->
    <button class="hamburger" [class.active]="isMenuOpen" (click)="toggleMenu()" aria-label="Toggle menu">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>
  </div>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay" [class.active]="isMenuOpen" (click)="closeMenu()"></div>
</nav>
