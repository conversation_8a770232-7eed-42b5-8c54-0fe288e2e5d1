<!-- Breadcrumb -->
<section class="breadcrumb-section">
  <div class="container">
    <nav class="breadcrumb">
      <a routerLink="/">Home</a>
      <span class="separator">/</span>
      <a routerLink="/shop">Shop</a>
      <span class="separator">/</span>
      <span class="current">{{ product?.name }}</span>
    </nav>
  </div>
</section>

<!-- Product Detail -->
<section class="product-detail-section" *ngIf="product">
  <div class="container">
    <div class="product-layout">
      <!-- Product Images -->
      <div class="product-images">
        <div class="main-image">
          <img [src]="selectedImage" [alt]="product.name" loading="lazy">
          <div class="image-badges" *ngIf="product.badges?.length">
            <span *ngFor="let badge of product.badges"
                  class="badge"
                  [ngClass]="badge.type">{{ badge.text }}</span>
          </div>
        </div>
        <div class="thumbnail-images" *ngIf="product?.images && product.images.length > 1">
          <button *ngFor="let image of product.images"
                  class="thumbnail"
                  [class.active]="selectedImage === image"
                  (click)="selectImage(image)">
            <img [src]="image" [alt]="product.name" loading="lazy">
          </button>
        </div>
      </div>

      <!-- Product Info -->
      <div class="product-info">
        <h1 class="product-title">{{ product.name }}</h1>

        <div class="product-rating" *ngIf="product.rating">
          <div class="stars">
            <span *ngFor="let star of [1,2,3,4,5]"
                  class="star"
                  [class.filled]="star <= product.rating">★</span>
          </div>
          <span class="rating-text">({{ product.reviewCount }} reviews)</span>
        </div>

        <div class="product-price">
          <span class="current-price">${{ product.price }}</span>
          <span class="original-price" *ngIf="product.originalPrice">${{ product.originalPrice }}</span>
          <span class="discount" *ngIf="product.originalPrice">
            {{ getDiscountPercentage() }}% OFF
          </span>
        </div>

        <div class="product-description">
          <p>{{ product.description }}</p>
        </div>

        <!-- Product Options -->
        <div class="product-options">
          <!-- Size Selection -->
          <div class="option-group">
            <label class="option-label">Size</label>
            <div class="size-options">
              <button *ngFor="let size of product.sizes"
                      class="size-option"
                      [class.active]="selectedSize === size"
                      [class.disabled]="!isSizeAvailable(size)"
                      (click)="selectSize(size)"
                      [disabled]="!isSizeAvailable(size)">
                {{ size }}
              </button>
            </div>
            <a href="#" class="size-guide">Size Guide</a>
          </div>

          <!-- Color Selection -->
          <div class="option-group" *ngIf="product.colors?.length">
            <label class="option-label">Color: {{ selectedColor?.name }}</label>
            <div class="color-options">
              <button *ngFor="let color of product.colors"
                      class="color-option"
                      [class.active]="selectedColor === color"
                      (click)="selectColor(color)"
                      [style.background-color]="color.hex"
                      [title]="color.name">
              </button>
            </div>
          </div>

          <!-- Quantity -->
          <div class="option-group">
            <label class="option-label">Quantity</label>
            <div class="quantity-selector">
              <button class="quantity-btn" (click)="decreaseQuantity()" [disabled]="quantity <= 1">-</button>
              <input type="number" [(ngModel)]="quantity" min="1" max="10" class="quantity-input">
              <button class="quantity-btn" (click)="increaseQuantity()" [disabled]="quantity >= 10">+</button>
            </div>
          </div>
        </div>

        <!-- Add to Cart -->
        <div class="product-actions">
          <button class="btn btn-primary add-to-cart"
                  (click)="addToCart()"
                  [disabled]="!selectedSize || !selectedColor">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"></path>
              <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"></path>
              <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"></path>
            </svg>
            Add to Cart - ${{ (product.price * quantity).toFixed(2) }}
          </button>

          <button class="btn btn-secondary wishlist-btn" (click)="toggleWishlist()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z"></path>
            </svg>
            {{ isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist' }}
          </button>
        </div>

        <!-- Product Features -->
        <div class="product-features">
          <div class="feature" *ngFor="let feature of product.features">
            <div class="feature-icon">{{ feature.icon }}</div>
            <div class="feature-text">
              <h4>{{ feature.title }}</h4>
              <p>{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Product Details Tabs -->
<section class="product-tabs-section">
  <div class="container">
    <div class="tabs-header">
      <button *ngFor="let tab of tabs"
              class="tab-btn"
              [class.active]="activeTab === tab.id"
              (click)="setActiveTab(tab.id)">
        {{ tab.label }}
      </button>
    </div>

    <div class="tabs-content">
      <div class="tab-panel" [class.active]="activeTab === 'description'">
        <h3>Product Description</h3>
        <div [innerHTML]="product?.fullDescription"></div>
      </div>

      <div class="tab-panel" [class.active]="activeTab === 'specifications'">
        <h3>Specifications</h3>
        <table class="specs-table">
          <tr *ngFor="let spec of product?.specifications">
            <td class="spec-label">{{ spec.label }}</td>
            <td class="spec-value">{{ spec.value }}</td>
          </tr>
        </table>
      </div>

      <div class="tab-panel" [class.active]="activeTab === 'reviews'">
        <h3>Customer Reviews</h3>
        <div class="reviews-summary">
          <div class="rating-overview">
            <div class="average-rating">{{ product?.rating }}</div>
            <div class="stars-large">
              <span *ngFor="let star of [1,2,3,4,5]"
                    class="star"
                    [class.filled]="star <= (product?.rating || 0)">★</span>
            </div>
            <p>Based on {{ product?.reviewCount }} reviews</p>
          </div>
        </div>

        <div class="reviews-list">
          <div class="review" *ngFor="let review of product?.reviews">
            <div class="review-header">
              <div class="reviewer-info">
                <img [src]="review.avatar" [alt]="review.name" class="reviewer-avatar">
                <div>
                  <h4>{{ review.name }}</h4>
                  <div class="review-stars">
                    <span *ngFor="let star of [1,2,3,4,5]"
                          class="star"
                          [class.filled]="star <= review.rating">★</span>
                  </div>
                </div>
              </div>
              <span class="review-date">{{ review.date | date }}</span>
            </div>
            <p class="review-text">{{ review.text }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Related Products -->
<section class="related-products-section">
  <div class="container">
    <h2 class="section-title">You might also like</h2>
    <div class="products-grid">
      <div class="product-card" *ngFor="let relatedProduct of relatedProducts" (click)="viewRelatedProduct(relatedProduct)">
        <div class="product-image">
          <img [src]="relatedProduct.image" [alt]="relatedProduct.name" loading="lazy">
          <div class="product-overlay">
            <button class="quick-view-btn" (click)="quickView(relatedProduct); $event.stopPropagation()">Quick View</button>
            <button class="add-to-cart-btn" (click)="addRelatedToCart(relatedProduct); $event.stopPropagation()">Add to Cart</button>
          </div>
          <button class="wishlist-btn"
                  (click)="toggleRelatedWishlist(relatedProduct); $event.stopPropagation()"
                  [class.active]="isInWishlist(relatedProduct.id)"
                  aria-label="Add to wishlist">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z"></path>
            </svg>
          </button>
        </div>
        <div class="product-info">
          <h3 class="product-name">{{ relatedProduct.name }}</h3>
          <div class="product-price">${{ relatedProduct.price }}</div>
        </div>
      </div>
    </div>
  </div>
</section>
