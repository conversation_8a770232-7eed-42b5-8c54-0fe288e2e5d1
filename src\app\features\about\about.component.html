<!-- Hero Section -->
<section class="about-hero">
  <div class="container">
    <div class="hero-content">
      <h1 class="hero-title">About ROOT</h1>
      <p class="hero-subtitle">We believe in the power of simplicity. Every thread, every stitch, every design choice is made with intention.</p>
    </div>
  </div>
</section>

<!-- Story Section -->
<section class="story-section">
  <div class="container">
    <div class="story-grid">
      <div class="story-text">
        <h2 class="section-title">Our Story</h2>
        <p>ROOT was born from a simple observation: men's fashion had become overcomplicated. We saw a world filled with flashy logos, unnecessary details, and fast fashion that prioritized quantity over quality.</p>
        <p>Founded in 2020, we set out to create something different. Something that would stand the test of time, both in style and construction. We wanted to get back to the roots of what makes great clothing - premium materials, thoughtful design, and ethical manufacturing.</p>
        <p>Today, ROOT represents more than just t-shirts. We're a movement toward conscious consumption, timeless style, and the belief that less can indeed be more.</p>
      </div>
      <div class="story-image">
        <img src="assets/images/about-story.jpg" alt="ROOT story" loading="lazy">
      </div>
    </div>
  </div>
</section>

<!-- Values Section -->
<section class="values-section">
  <div class="container">
    <h2 class="section-title">Our Values</h2>
    <div class="values-grid">
      <div class="value-card">
        <div class="value-icon">🌱</div>
        <h3>Sustainability</h3>
        <p>We use only organic cotton sourced from certified sustainable farms. Our packaging is 100% recyclable, and we offset our carbon footprint.</p>
      </div>
      <div class="value-card">
        <div class="value-icon">🤝</div>
        <h3>Ethical Manufacturing</h3>
        <p>Every ROOT product is made in facilities that provide fair wages, safe working conditions, and respect for workers' rights.</p>
      </div>
      <div class="value-card">
        <div class="value-icon">✨</div>
        <h3>Quality First</h3>
        <p>We believe in making fewer, better things. Each piece is designed to last, reducing waste and providing lasting value.</p>
      </div>
      <div class="value-card">
        <div class="value-icon">🎯</div>
        <h3>Minimalist Design</h3>
        <p>Clean lines, perfect fits, and timeless aesthetics. Our designs transcend trends to create pieces you'll love for years.</p>
      </div>
    </div>
  </div>
</section>

<!-- Team Section -->
<section class="team-section">
  <div class="container">
    <h2 class="section-title">Meet the Team</h2>
    <div class="team-grid">
      <div class="team-member" *ngFor="let member of teamMembers">
        <div class="member-image">
          <img [src]="member.image" [alt]="member.name" loading="lazy">
        </div>
        <div class="member-info">
          <h3>{{ member.name }}</h3>
          <p class="member-role">{{ member.role }}</p>
          <p class="member-bio">{{ member.bio }}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Process Section -->
<section class="process-section">
  <div class="container">
    <h2 class="section-title">Our Process</h2>
    <div class="process-timeline">
      <div class="process-step" *ngFor="let step of processSteps; let i = index">
        <div class="step-number">{{ i + 1 }}</div>
        <div class="step-content">
          <h3>{{ step.title }}</h3>
          <p>{{ step.description }}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Impact Section -->
<section class="impact-section">
  <div class="container">
    <h2 class="section-title">Our Impact</h2>
    <div class="impact-stats">
      <div class="stat-card" *ngFor="let stat of impactStats">
        <div class="stat-number">{{ stat.number }}</div>
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-description">{{ stat.description }}</div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
  <div class="container">
    <div class="cta-content">
      <h2>Ready to Experience ROOT?</h2>
      <p>Join thousands of men who've discovered the perfect balance of style, comfort, and conscience.</p>
      <div class="cta-buttons">
        <a routerLink="/shop" class="btn btn-primary">Shop Collection</a>
        <a href="#contact" class="btn btn-secondary">Get in Touch</a>
      </div>
    </div>
  </div>
</section>
