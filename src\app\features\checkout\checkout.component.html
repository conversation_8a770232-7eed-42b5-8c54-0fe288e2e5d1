<!-- Breadcrumb -->
<section class="breadcrumb-section">
  <div class="container">
    <nav class="breadcrumb">
      <a routerLink="/">Home</a>
      <span class="separator">/</span>
      <a routerLink="/cart">Cart</a>
      <span class="separator">/</span>
      <span class="current">Checkout</span>
    </nav>
  </div>
</section>

<!-- Checkout Section -->
<section class="checkout-section">
  <div class="container">
    <div class="checkout-header">
      <h1>Checkout</h1>
      <div class="checkout-steps">
        <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
          <span class="step-number">1</span>
          <span class="step-label">Shipping</span>
        </div>
        <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
          <span class="step-number">2</span>
          <span class="step-label">Payment</span>
        </div>
        <div class="step" [class.active]="currentStep >= 3">
          <span class="step-number">3</span>
          <span class="step-label">Review</span>
        </div>
      </div>
    </div>

    <div class="checkout-content">
      <!-- Left Column - Forms -->
      <div class="checkout-forms">
        <!-- Step 1: Shipping Information -->
        <div class="checkout-step" *ngIf="currentStep === 1">
          <h2>Shipping Information</h2>
          <form class="checkout-form" (ngSubmit)="nextStep()" #shippingForm="ngForm">
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name *</label>
                <input type="text" id="firstName" name="firstName" [(ngModel)]="shippingInfo.firstName" required class="form-input">
              </div>
              <div class="form-group">
                <label for="lastName">Last Name *</label>
                <input type="text" id="lastName" name="lastName" [(ngModel)]="shippingInfo.lastName" required class="form-input">
              </div>
            </div>

            <div class="form-group">
              <label for="email">Email Address *</label>
              <input type="email" id="email" name="email" [(ngModel)]="shippingInfo.email" required class="form-input">
            </div>

            <div class="form-group">
              <label for="phone">Phone Number *</label>
              <input type="tel" id="phone" name="phone" [(ngModel)]="shippingInfo.phone" required class="form-input">
            </div>

            <div class="form-group">
              <label for="address">Street Address *</label>
              <input type="text" id="address" name="address" [(ngModel)]="shippingInfo.address" required class="form-input">
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="city">City *</label>
                <input type="text" id="city" name="city" [(ngModel)]="shippingInfo.city" required class="form-input">
              </div>
              <div class="form-group">
                <label for="state">State *</label>
                <select id="state" name="state" [(ngModel)]="shippingInfo.state" required class="form-input">
                  <option value="">Select State</option>
                  <option value="CA">California</option>
                  <option value="NY">New York</option>
                  <option value="TX">Texas</option>
                  <option value="FL">Florida</option>
                  <!-- Add more states as needed -->
                </select>
              </div>
              <div class="form-group">
                <label for="zipCode">ZIP Code *</label>
                <input type="text" id="zipCode" name="zipCode" [(ngModel)]="shippingInfo.zipCode" required class="form-input">
              </div>
            </div>

            <div class="form-group">
              <label for="country">Country *</label>
              <select id="country" name="country" [(ngModel)]="shippingInfo.country" required class="form-input">
                <option value="US">United States</option>
                <option value="CA">Canada</option>
                <option value="UK">United Kingdom</option>
                <!-- Add more countries as needed -->
              </select>
            </div>

            <div class="shipping-options">
              <h3>Shipping Method</h3>
              <div class="shipping-option" *ngFor="let option of shippingOptions">
                <label class="radio-label">
                  <input type="radio" name="shippingMethod" [value]="option.id" [(ngModel)]="selectedShippingMethod">
                  <span class="radio-custom"></span>
                  <div class="option-details">
                    <span class="option-name">{{ option.name }}</span>
                    <span class="option-time">{{ option.time }}</span>
                  </div>
                  <span class="option-price">${{ option.price }}</span>
                </label>
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn btn-primary" [disabled]="!shippingForm.valid">
                Continue to Payment
              </button>
            </div>
          </form>
        </div>

        <!-- Step 2: Payment Information -->
        <div class="checkout-step" *ngIf="currentStep === 2">
          <h2>Payment Information</h2>
          <form class="checkout-form" (ngSubmit)="nextStep()" #paymentForm="ngForm">
            <div class="payment-methods">
              <div class="payment-method" *ngFor="let method of paymentMethods">
                <label class="radio-label">
                  <input type="radio" name="paymentMethod" [value]="method.id" [(ngModel)]="selectedPaymentMethod">
                  <span class="radio-custom"></span>
                  <span class="method-name">{{ method.name }}</span>
                  <div class="method-icons">
                    <img *ngFor="let icon of method.icons" [src]="icon" [alt]="method.name" class="payment-icon">
                  </div>
                </label>
              </div>
            </div>

            <div class="card-details" *ngIf="selectedPaymentMethod === 'card'">
              <div class="form-group">
                <label for="cardNumber">Card Number *</label>
                <input type="text" id="cardNumber" name="cardNumber" [(ngModel)]="paymentInfo.cardNumber"
                       placeholder="1234 5678 9012 3456" required class="form-input">
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="expiryDate">Expiry Date *</label>
                  <input type="text" id="expiryDate" name="expiryDate" [(ngModel)]="paymentInfo.expiryDate"
                         placeholder="MM/YY" required class="form-input">
                </div>
                <div class="form-group">
                  <label for="cvv">CVV *</label>
                  <input type="text" id="cvv" name="cvv" [(ngModel)]="paymentInfo.cvv"
                         placeholder="123" required class="form-input">
                </div>
              </div>

              <div class="form-group">
                <label for="cardName">Name on Card *</label>
                <input type="text" id="cardName" name="cardName" [(ngModel)]="paymentInfo.cardName" required class="form-input">
              </div>
            </div>

            <div class="billing-address">
              <label class="checkbox-label">
                <input type="checkbox" [(ngModel)]="sameAsShipping" name="sameAsShipping">
                <span class="checkmark"></span>
                Billing address same as shipping
              </label>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="previousStep()">
                Back to Shipping
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="!paymentForm.valid">
                Review Order
              </button>
            </div>
          </form>
        </div>

        <!-- Step 3: Order Review -->
        <div class="checkout-step" *ngIf="currentStep === 3">
          <h2>Review Your Order</h2>
          <div class="order-review">
            <div class="review-section">
              <h3>Shipping Information</h3>
              <div class="review-details">
                <p><strong>{{ shippingInfo.firstName }} {{ shippingInfo.lastName }}</strong></p>
                <p>{{ shippingInfo.address }}</p>
                <p>{{ shippingInfo.city }}, {{ shippingInfo.state }} {{ shippingInfo.zipCode }}</p>
                <p>{{ shippingInfo.country }}</p>
                <p>{{ shippingInfo.email }}</p>
                <p>{{ shippingInfo.phone }}</p>
              </div>
              <button type="button" class="btn-edit" (click)="goToStep(1)">Edit</button>
            </div>

            <div class="review-section">
              <h3>Payment Method</h3>
              <div class="review-details">
                <p *ngIf="selectedPaymentMethod === 'card'">
                  <strong>Credit Card</strong><br>
                  **** **** **** {{ paymentInfo.cardNumber.slice(-4) }}
                </p>
                <p *ngIf="selectedPaymentMethod === 'paypal'">
                  <strong>PayPal</strong>
                </p>
              </div>
              <button type="button" class="btn-edit" (click)="goToStep(2)">Edit</button>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="previousStep()">
                Back to Payment
              </button>
              <button type="button" class="btn btn-primary" (click)="placeOrder()" [disabled]="isProcessing">
                <span *ngIf="!isProcessing">Place Order</span>
                <span *ngIf="isProcessing" class="loading-spinner">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                  </svg>
                  Processing...
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Order Summary -->
      <div class="order-summary">
        <h3>Order Summary</h3>
        <div class="summary-items">
          <div class="summary-item" *ngFor="let item of cartItems">
            <div class="item-image">
              <img [src]="item.image" [alt]="item.name" loading="lazy">
            </div>
            <div class="item-details">
              <h4>{{ item.name }}</h4>
              <p>Size: {{ item.size }} | Color: {{ item.color }}</p>
              <p>Qty: {{ item.quantity }}</p>
            </div>
            <div class="item-price">
              ${{ (item.price * item.quantity).toFixed(2) }}
            </div>
          </div>
        </div>

        <div class="summary-totals">
          <div class="total-line">
            <span>Subtotal</span>
            <span>${{ subtotal.toFixed(2) }}</span>
          </div>
          <div class="total-line">
            <span>Shipping</span>
            <span>${{ shippingCost.toFixed(2) }}</span>
          </div>
          <div class="total-line">
            <span>Tax</span>
            <span>${{ tax.toFixed(2) }}</span>
          </div>
          <div class="total-line total">
            <span>Total</span>
            <span>${{ total.toFixed(2) }}</span>
          </div>
        </div>

        <div class="promo-code">
          <input type="text" placeholder="Promo code" [(ngModel)]="promoCode" class="promo-input">
          <button type="button" class="btn btn-secondary" (click)="applyPromoCode()">Apply</button>
        </div>
      </div>
    </div>
  </div>
</section>
