import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MetaService } from '../../../shared/services/meta.service';

interface LoginForm {
  email: string;
  password: string;
}

@Component({
  selector: 'app-login',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit {
  formData: LoginForm = {
    email: '',
    password: ''
  };

  showPassword = false;
  rememberMe = false;
  isLoading = false;
  loginError = '';

  constructor(
    private router: Router,
    private metaService: MetaService
  ) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'Sign In',
      description: 'Sign in to your ROOT account to access exclusive features and track your orders.',
      keywords: 'login, sign in, ROOT account, authentication',
      url: window.location.href
    });
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.isLoading) return;

    this.isLoading = true;
    this.loginError = '';

    // Simulate API call
    setTimeout(() => {
      // Mock authentication logic
      if (this.formData.email === '<EMAIL>' && this.formData.password === 'password') {
        console.log('Login successful');
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('userEmail', this.formData.email);
        this.router.navigate(['/']);
      } else {
        this.loginError = 'Invalid email or password. Try <EMAIL> / password';
      }
      this.isLoading = false;
    }, 1500);
  }

  loginWithGoogle() {
    console.log('Login with Google');
    // Implement Google OAuth
  }

  loginWithFacebook() {
    console.log('Login with Facebook');
    // Implement Facebook OAuth
  }
}
