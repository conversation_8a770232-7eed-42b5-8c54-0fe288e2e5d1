import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MetaService } from '../../../shared/services/meta.service';

interface SignupForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
}

@Component({
  selector: 'app-signup',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './signup.component.html',
  styleUrl: './signup.component.scss'
})
export class SignupComponent implements OnInit {
  formData: SignupForm = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  };

  showPassword = false;
  acceptTerms = false;
  subscribeNewsletter = true;
  isLoading = false;
  signupError = '';
  passwordStrength = 0;

  constructor(
    private router: Router,
    private metaService: MetaService
  ) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'Sign Up',
      description: 'Create your ROOT account to access exclusive features, track orders, and discover premium essentials.',
      keywords: 'signup, register, create account, ROOT membership',
      url: window.location.href
    });

    // Watch for password changes to calculate strength
    this.calculatePasswordStrength();
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  get passwordMismatch(): boolean {
    return this.formData.password !== this.formData.confirmPassword &&
           this.formData.confirmPassword.length > 0;
  }

  calculatePasswordStrength() {
    const password = this.formData.password;
    let strength = 0;

    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/\d/.test(password)) strength += 12.5;
    if (/[@$!%*?&]/.test(password)) strength += 12.5;

    this.passwordStrength = Math.min(strength, 100);
  }

  getPasswordStrengthClass(): string {
    if (this.passwordStrength < 25) return 'weak';
    if (this.passwordStrength < 50) return 'fair';
    if (this.passwordStrength < 75) return 'good';
    return 'strong';
  }

  getPasswordStrengthText(): string {
    if (this.passwordStrength < 25) return 'Weak';
    if (this.passwordStrength < 50) return 'Fair';
    if (this.passwordStrength < 75) return 'Good';
    return 'Strong';
  }

  onSubmit() {
    if (this.isLoading || this.passwordMismatch) return;

    this.isLoading = true;
    this.signupError = '';

    // Simulate API call
    setTimeout(() => {
      // Mock registration logic
      if (this.formData.email === '<EMAIL>') {
        this.signupError = 'An account with this email already exists';
      } else {
        console.log('Signup successful', this.formData);
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('userEmail', this.formData.email);
        localStorage.setItem('userName', `${this.formData.firstName} ${this.formData.lastName}`);
        this.router.navigate(['/']);
      }
      this.isLoading = false;
    }, 2000);
  }

  signupWithGoogle() {
    console.log('Signup with Google');
    // Implement Google OAuth
  }

  signupWithFacebook() {
    console.log('Signup with Facebook');
    // Implement Facebook OAuth
  }
}
