<!-- Breadcrumb -->
<section class="breadcrumb-section">
  <div class="container">
    <nav class="breadcrumb">
      <a routerLink="/">Home</a>
      <span class="separator">/</span>
      <span class="current">Wishlist</span>
    </nav>
  </div>
</section>

<!-- Wishlist Content -->
<section class="wishlist-section">
  <div class="container">
    <div class="wishlist-header">
      <h1>My Wishlist</h1>
      <p *ngIf="wishlistItems.length > 0">{{ wishlistItems.length }} item{{ wishlistItems.length !== 1 ? 's' : '' }} saved</p>
      <p *ngIf="wishlistItems.length === 0">Your wishlist is empty</p>
    </div>

    <!-- Empty State -->
    <div class="empty-wishlist" *ngIf="wishlistItems.length === 0">
      <div class="empty-icon">
        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
          <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z"></path>
        </svg>
      </div>
      <h2>Your wishlist is empty</h2>
      <p>Save items you love to your wishlist and shop them later.</p>
      <a routerLink="/shop" class="btn btn-primary">Start Shopping</a>
    </div>

    <!-- Wishlist Items -->
    <div class="wishlist-grid" *ngIf="wishlistItems.length > 0">
      <div class="wishlist-item" *ngFor="let item of wishlistItems; trackBy: trackByItem">
        <div class="item-image" (click)="viewProduct(item)">
          <img [src]="item.image" [alt]="item.name" loading="lazy">
          <div class="item-badges" *ngIf="item.isNew || item.onSale">
            <span class="badge new" *ngIf="item.isNew">New</span>
            <span class="badge sale" *ngIf="item.onSale">Sale</span>
          </div>
          <button class="remove-btn" (click)="removeFromWishlist(item); $event.stopPropagation()" aria-label="Remove from wishlist">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div class="item-info" (click)="viewProduct(item)">
          <h3 class="item-name">{{ item.name }}</h3>
          <div class="item-price">
            <span class="current-price">${{ item.price }}</span>
            <span class="original-price" *ngIf="item.originalPrice">${{ item.originalPrice }}</span>
          </div>
          <div class="item-colors">
            <span *ngFor="let color of item.colors"
                  class="color-dot"
                  [style.background-color]="color.hex"
                  [title]="color.name"></span>
          </div>
          <div class="item-rating">
            <div class="stars">
              <span *ngFor="let star of [1,2,3,4,5]"
                    class="star"
                    [class.filled]="star <= item.rating">★</span>
            </div>
            <span class="rating-count">({{ item.reviewCount }})</span>
          </div>
        </div>

        <div class="item-actions">
          <button class="btn btn-primary" (click)="addToCart(item)">Add to Cart</button>
          <button class="btn btn-secondary" (click)="removeFromWishlist(item)">Remove</button>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="wishlist-actions" *ngIf="wishlistItems.length > 0">
      <button class="btn btn-secondary" (click)="clearWishlist()">Clear Wishlist</button>
      <button class="btn btn-primary" (click)="addAllToCart()">Add All to Cart</button>
    </div>
  </div>
</section>
