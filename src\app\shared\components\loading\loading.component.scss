.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 200px;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 1rem;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #000;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;

  &:nth-child(2) {
    animation-delay: -0.4s;
    border-top-color: #666;
  }

  &:nth-child(3) {
    animation-delay: -0.8s;
    border-top-color: #999;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
  text-align: center;
}
