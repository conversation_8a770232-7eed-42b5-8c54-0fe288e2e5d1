// Global styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #000;
  letter-spacing: -0.02em;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 0;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;

  &.btn-primary {
    background: #000;
    color: white;

    &:hover {
      background: #333;
      transform: translateY(-2px);
    }
  }

  &.btn-secondary {
    background: transparent;
    color: #000;
    border: 2px solid #000;

    &:hover {
      background: #000;
      color: white;
      transform: translateY(-2px);
    }
  }

  &.btn-outline {
    background: transparent;
    color: #333;
    border: 1px solid #333;

    &:hover {
      background: #333;
      color: white;
    }
  }
}

// Hero Section
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 80px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.hero-text {
  z-index: 2;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #000;

  .title-line {
    display: block;

    &.highlight {
      color: #666;
      font-style: italic;
    }
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #666;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-3d {
  height: 500px;
  position: relative;

  .three-container {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #666;
  transition: color 0.3s ease;

  &:hover {
    color: #000;
  }

  .scroll-text {
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  .scroll-arrow {
    animation: bounce 2s infinite;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// Categories Section
.categories-section {
  padding: 6rem 0;
  background: white;
}

.categories-scroll {
  display: flex;
  gap: 2rem;
  overflow-x: auto;
  padding: 1rem 0;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;

    &:hover {
      background: #555;
    }
  }
}

.category-card {
  min-width: 280px;
  height: 350px;
  background-size: cover;
  background-position: center;
  border-radius: 15px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-10px) rotateX(5deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

    .category-overlay {
      background: rgba(0, 0, 0, 0.7);
    }
  }
}

.category-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  transition: all 0.3s ease;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.category-count {
  font-size: 0.9rem;
  opacity: 0.9;
}

// Featured Products Section
.featured-section {
  padding: 6rem 0;
  background: #f8f9fa;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

    .product-overlay {
      opacity: 1;
      visibility: visible;
    }
  }
}

.product-image {
  position: relative;
  height: 300px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.1);
  }
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.quick-add-btn {
  background: white;
  color: #000;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #000;
    color: white;
    transform: scale(1.05);
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.product-info {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
}

.section-footer {
  text-align: center;
}

// Brand Story Section
.brand-story-section {
  padding: 6rem 0;
  background: white;
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.story-image {
  img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
  }
}

.story-text {
  .section-title {
    text-align: left;
    margin-bottom: 2rem;
  }
}

.story-points {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.story-point {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.point-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.point-content {
  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #000;
  }

  p {
    color: #666;
    line-height: 1.6;
  }
}

// Reviews Section
.reviews-section {
  padding: 6rem 0;
  background: #f8f9fa;
}

.reviews-carousel {
  display: flex;
  gap: 2rem;
  overflow-x: auto;
  padding: 1rem 0;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;

    &:hover {
      background: #555;
    }
  }
}

.review-card {
  min-width: 350px;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.review-stars {
  margin-bottom: 1rem;

  .star {
    color: #ddd;
    font-size: 1.25rem;

    &.filled {
      color: #ffc107;
    }
  }
}

.review-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.review-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-weight: 600;
  color: #000;
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: 0.9rem;
  color: #666;
}

// Instagram Section
.instagram-section {
  padding: 6rem 0;
  background: white;
}

.instagram-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.instagram-item {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);

    .instagram-overlay {
      opacity: 1;
      visibility: visible;
    }

    img {
      transform: scale(1.1);
    }
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.instagram-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  svg {
    width: 30px;
    height: 30px;
  }
}

// Newsletter Section
.newsletter-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: white;
  text-align: center;
}

.newsletter-content {
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.newsletter-subtitle {
  font-size: 1.1rem;
  color: #ccc;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.newsletter-form {
  .form-group {
    display: flex;
    gap: 0;
    max-width: 500px;
    margin: 0 auto;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
}

.newsletter-input {
  flex: 1;
  padding: 1.25rem 1.5rem;
  border: none;
  font-size: 1rem;
  outline: none;
  background: white;
  color: #000;

  &::placeholder {
    color: #999;
  }
}

.newsletter-btn {
  padding: 1.25rem 2rem;
  background: #666;
  color: white;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: #555;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.form-validation {
  margin-top: 1rem;
  text-align: left;

  p {
    color: #ff6b6b;
    font-size: 0.9rem;
    margin: 0.25rem 0;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .hero-content {
    gap: 2rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .story-content {
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  // Hero Section Mobile
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-3d {
    height: 300px;
  }

  .hero-buttons {
    justify-content: center;
  }

  // Categories Mobile
  .categories-scroll {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .category-card {
    min-width: 250px;
    height: 300px;
  }

  // Products Mobile
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  // Story Mobile
  .story-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .story-image {
    order: -1;
  }

  // Reviews Mobile
  .review-card {
    min-width: 300px;
  }

  // Instagram Mobile
  .instagram-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  // Newsletter Mobile
  .newsletter-title {
    font-size: 2rem;
  }

  .newsletter-form .form-group {
    flex-direction: column;
    border-radius: 0;
  }

  .newsletter-input,
  .newsletter-btn {
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
    min-width: 120px;
  }

  .category-card {
    min-width: 220px;
    height: 280px;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .review-card {
    min-width: 280px;
    padding: 1.5rem;
  }

  .instagram-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}
