import { <PERSON>mpo<PERSON>, <PERSON>ementRef, ViewChild, On<PERSON>nit, On<PERSON>estroy, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import * as THREE from 'three';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { MetaService } from '../../shared/services/meta.service';
import { CartService } from '../../shared/services/cart.service';
import { WishlistService } from '../../shared/services/wishlist.service';

interface Category {
  name: string;
  slug: string;
  image: string;
  count: number;
}

interface Product {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  colors: Array<{name: string; value: string; hex: string}>;
  sizes: string[];
  rating: number;
  reviewCount: number;
  isNew?: boolean;
  onSale?: boolean;
  category: string;
}

interface Review {
  id: number;
  name: string;
  title: string;
  text: string;
  rating: number;
  avatar: string;
}

interface InstagramPost {
  id: string;
  image: string;
  url: string;
}

@Component({
  selector: 'app-home',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('threeContainer', { static: false }) threeContainer!: ElementRef;
  @ViewChild('reviewsCarousel', { static: false }) reviewsCarousel!: ElementRef;

  constructor(
    private metaService: MetaService,
    private router: Router,
    private cartService: CartService,
    private wishlistService: WishlistService
  ) {}

  // Three.js properties
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private renderer!: THREE.WebGLRenderer;
  private tshirt!: THREE.Mesh;
  private animationId!: number;

  // Loading states
  isLoading = true;
  hasError = false;
  errorMessage = '';

  // Component data
  categories: Category[] = [
    { name: 'Casual', slug: 'casual', image: 'assets/images/category-casual.jpg', count: 4 },
    { name: 'Urban', slug: 'urban', image: 'assets/images/category-urban.jpg', count: 2 },
    { name: 'Premium', slug: 'premium', image: 'assets/images/category-premium.jpg', count: 1 },
    { name: 'Oversized', slug: 'oversized', image: 'assets/images/category-oversized.jpg', count: 1 },
    { name: 'Sporty', slug: 'sporty', image: 'assets/images/category-sporty.jpg', count: 0 }
  ];

  featuredProducts: Product[] = [
    {
      id: 1,
      name: 'Essential White Tee',
      price: 29.99,
      originalPrice: 39.99,
      image: 'assets/images/product-1.jpg',
      colors: [
        { name: 'White', value: 'white', hex: '#FFFFFF' },
        { name: 'Black', value: 'black', hex: '#000000' }
      ],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      rating: 5,
      reviewCount: 124,
      isNew: true,
      onSale: true,
      category: 'casual'
    },
    {
      id: 2,
      name: 'Classic Black Tee',
      price: 29.99,
      image: 'assets/images/product-2.jpg',
      colors: [
        { name: 'Black', value: 'black', hex: '#000000' },
        { name: 'Gray', value: 'gray', hex: '#808080' }
      ],
      sizes: ['S', 'M', 'L', 'XL'],
      rating: 4,
      reviewCount: 89,
      category: 'casual'
    },
    {
      id: 3,
      name: 'Vintage Gray Tee',
      price: 34.99,
      image: 'assets/images/product-3.jpg',
      colors: [
        { name: 'Gray', value: 'gray', hex: '#808080' },
        { name: 'Charcoal', value: 'charcoal', hex: '#36454F' }
      ],
      sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
      rating: 5,
      reviewCount: 67,
      isNew: true,
      category: 'vintage'
    },
    {
      id: 4,
      name: 'Premium Navy Tee',
      price: 39.99,
      image: 'assets/images/product-4.jpg',
      colors: [
        { name: 'Navy', value: 'navy', hex: '#000080' },
        { name: 'Royal Blue', value: 'royal-blue', hex: '#4169E1' }
      ],
      sizes: ['S', 'M', 'L', 'XL'],
      rating: 5,
      reviewCount: 156,
      category: 'premium'
    },
    {
      id: 5,
      name: 'Oversized Cream Tee',
      price: 32.99,
      originalPrice: 42.99,
      image: 'assets/images/product-5.jpg',
      colors: [
        { name: 'Cream', value: 'cream', hex: '#F5F5DC' },
        { name: 'Beige', value: 'beige', hex: '#F5F5DC' }
      ],
      sizes: ['M', 'L', 'XL', 'XXL'],
      rating: 4,
      reviewCount: 43,
      onSale: true,
      category: 'oversized'
    },
    {
      id: 6,
      name: 'Minimalist Olive Tee',
      price: 31.99,
      image: 'assets/images/product-6.jpg',
      colors: [
        { name: 'Olive', value: 'olive', hex: '#808000' },
        { name: 'Forest Green', value: 'forest-green', hex: '#228B22' }
      ],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      rating: 4,
      reviewCount: 78,
      category: 'casual'
    },
    {
      id: 7,
      name: 'Urban Charcoal Tee',
      price: 33.99,
      image: 'assets/images/product-7.jpg',
      colors: [
        { name: 'Charcoal', value: 'charcoal', hex: '#36454F' },
        { name: 'Dark Gray', value: 'dark-gray', hex: '#A9A9A9' }
      ],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      rating: 5,
      reviewCount: 92,
      category: 'urban'
    },
    {
      id: 8,
      name: 'Classic Burgundy Tee',
      price: 29.99,
      image: 'assets/images/product-8.jpg',
      colors: [
        { name: 'Burgundy', value: 'burgundy', hex: '#800020' },
        { name: 'Wine', value: 'wine', hex: '#722F37' }
      ],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      rating: 4,
      reviewCount: 134,
      category: 'casual'
    }
  ];

  customerReviews: Review[] = [
    {
      id: 1,
      name: 'Alex Johnson',
      title: 'Verified Customer',
      text: 'The quality is incredible! These tees are so comfortable and the fit is perfect. Definitely ordering more.',
      rating: 5,
      avatar: 'assets/images/avatar-1.jpg'
    },
    {
      id: 2,
      name: 'Marcus Chen',
      title: 'Repeat Customer',
      text: 'ROOT has become my go-to brand for basics. The attention to detail and sustainable practices make it worth every penny.',
      rating: 5,
      avatar: 'assets/images/avatar-2.jpg'
    },
    {
      id: 3,
      name: 'David Rodriguez',
      title: 'Style Enthusiast',
      text: 'Finally found tees that look great and feel amazing. The minimalist design is exactly what I was looking for.',
      rating: 5,
      avatar: 'assets/images/avatar-3.jpg'
    }
  ];

  instagramPosts: InstagramPost[] = [
    { id: '1', image: 'assets/images/instagram-1.jpg', url: 'https://instagram.com/p/example1' },
    { id: '2', image: 'assets/images/instagram-2.jpg', url: 'https://instagram.com/p/example2' },
    { id: '3', image: 'assets/images/instagram-3.jpg', url: 'https://instagram.com/p/example3' },
    { id: '4', image: 'assets/images/instagram-4.jpg', url: 'https://instagram.com/p/example4' },
    { id: '5', image: 'assets/images/instagram-5.jpg', url: 'https://instagram.com/p/example5' },
    { id: '6', image: 'assets/images/instagram-6.jpg', url: 'https://instagram.com/p/example6' }
  ];

  // Newsletter form
  newsletterEmail = '';

  ngOnInit() {
    // Set SEO meta tags
    this.metaService.updatePageMeta({
      title: 'Home',
      description: 'Elevate your basics with ROOT - Premium men\'s t-shirts designed for the modern minimalist. Minimal. Modern. Masculine.',
      keywords: 'men\'s t-shirts, premium clothing, minimalist fashion, organic cotton, sustainable fashion, ROOT brand',
      image: '/assets/images/og-home.jpg',
      url: window.location.href
    });

    // Register GSAP plugins
    gsap.registerPlugin(ScrollTrigger);

    // Simulate loading
    setTimeout(() => {
      this.isLoading = false;
    }, 1500);
  }

  ngAfterViewInit() {
    this.init3DScene();
    this.initAnimations();
  }

  ngOnDestroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    if (this.renderer) {
      this.renderer.dispose();
    }
    // Clean up GSAP animations
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
  }

  private init3DScene() {
    try {
      if (!this.threeContainer) return;

      const container = this.threeContainer.nativeElement;
      const width = container.clientWidth;
      const height = container.clientHeight;

    // Scene
    this.scene = new THREE.Scene();

    // Camera
    this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    this.camera.position.z = 5;

    // Renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(width, height);
    this.renderer.setClearColor(0x000000, 0);
    container.appendChild(this.renderer.domElement);

    // Create T-shirt geometry (simplified as a box for now)
    const geometry = new THREE.BoxGeometry(2, 2.5, 0.1);
    const material = new THREE.MeshPhongMaterial({
      color: 0x333333,
      shininess: 100
    });
    this.tshirt = new THREE.Mesh(geometry, material);
    this.scene.add(this.tshirt);

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    this.scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    this.scene.add(directionalLight);

    // Start animation
    this.animate();

      // Handle resize
      window.addEventListener('resize', () => this.onWindowResize());
    } catch (error) {
      console.error('Error initializing 3D scene:', error);
      this.hasError = true;
      this.errorMessage = 'Failed to load 3D content';
    }
  }

  private animate() {
    this.animationId = requestAnimationFrame(() => this.animate());

    // Rotate the T-shirt
    if (this.tshirt) {
      this.tshirt.rotation.y += 0.01;
      this.tshirt.rotation.x = Math.sin(Date.now() * 0.001) * 0.1;
    }

    this.renderer.render(this.scene, this.camera);
  }

  private onWindowResize() {
    if (!this.threeContainer) return;

    const container = this.threeContainer.nativeElement;
    const width = container.clientWidth;
    const height = container.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  navigateToCategory(slug: string) {
    // Navigate to shop with category filter
    this.router.navigate(['/shop'], { queryParams: { category: slug } });
  }

  addToCart(product: Product) {
    const cartItem = {
      productId: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      size: product.sizes[0], // Default to first available size
      color: product.colors[0].name, // Default to first available color
      quantity: 1
    };

    this.cartService.addToCart(cartItem);
    console.log('Added to cart from home:', cartItem);
  }

  openInstagramPost(url: string) {
    window.open(url, '_blank');
  }

  subscribeNewsletter() {
    if (this.newsletterEmail) {
      console.log('Subscribe newsletter:', this.newsletterEmail);
      // This would typically call an API to subscribe
      this.newsletterEmail = '';
      // Show success message
    }
  }

  retryLoad() {
    this.hasError = false;
    this.errorMessage = '';
    this.isLoading = true;

    setTimeout(() => {
      this.init3DScene();
      this.isLoading = false;
    }, 1000);
  }

  private initAnimations() {
    // Hero text animations
    gsap.fromTo('.hero-title .title-line',
      {
        y: 100,
        opacity: 0
      },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: 'power3.out',
        delay: 0.5
      }
    );

    gsap.fromTo('.hero-subtitle',
      {
        y: 50,
        opacity: 0
      },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: 'power3.out',
        delay: 1.2
      }
    );

    gsap.fromTo('.hero-buttons',
      {
        y: 50,
        opacity: 0
      },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: 'power3.out',
        delay: 1.5
      }
    );

    // Section title animations
    gsap.utils.toArray('.section-title').forEach((title: any) => {
      gsap.fromTo(title,
        {
          y: 50,
          opacity: 0
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: title,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    // Category cards animation
    gsap.utils.toArray('.category-card').forEach((card: any, index: number) => {
      gsap.fromTo(card,
        {
          y: 100,
          opacity: 0,
          rotateX: 15
        },
        {
          y: 0,
          opacity: 1,
          rotateX: 0,
          duration: 1,
          ease: 'power3.out',
          delay: index * 0.1,
          scrollTrigger: {
            trigger: card,
            start: 'top 85%',
            end: 'bottom 15%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    // Product cards animation
    gsap.utils.toArray('.product-card').forEach((card: any, index: number) => {
      gsap.fromTo(card,
        {
          y: 80,
          opacity: 0,
          scale: 0.9
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          ease: 'power3.out',
          delay: index * 0.1,
          scrollTrigger: {
            trigger: card,
            start: 'top 85%',
            end: 'bottom 15%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    // Story points animation
    gsap.utils.toArray('.story-point').forEach((point: any, index: number) => {
      gsap.fromTo(point,
        {
          x: -50,
          opacity: 0
        },
        {
          x: 0,
          opacity: 1,
          duration: 1,
          ease: 'power3.out',
          delay: index * 0.2,
          scrollTrigger: {
            trigger: point,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    // Review cards animation
    gsap.utils.toArray('.review-card').forEach((card: any, index: number) => {
      gsap.fromTo(card,
        {
          y: 60,
          opacity: 0,
          rotateY: 15
        },
        {
          y: 0,
          opacity: 1,
          rotateY: 0,
          duration: 1,
          ease: 'power3.out',
          delay: index * 0.15,
          scrollTrigger: {
            trigger: card,
            start: 'top 85%',
            end: 'bottom 15%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    // Instagram items animation
    gsap.utils.toArray('.instagram-item').forEach((item: any, index: number) => {
      gsap.fromTo(item,
        {
          scale: 0.8,
          opacity: 0,
          rotation: 5
        },
        {
          scale: 1,
          opacity: 1,
          rotation: 0,
          duration: 0.8,
          ease: 'back.out(1.7)',
          delay: index * 0.1,
          scrollTrigger: {
            trigger: item,
            start: 'top 90%',
            end: 'bottom 10%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    });

    // Newsletter section animation
    gsap.fromTo('.newsletter-content',
      {
        y: 80,
        opacity: 0
      },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: '.newsletter-section',
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );

    // Parallax effect for hero background
    gsap.to('.hero-section', {
      yPercent: -50,
      ease: 'none',
      scrollTrigger: {
        trigger: '.hero-section',
        start: 'top bottom',
        end: 'bottom top',
        scrub: true
      }
    });
  }

  viewProduct(product: Product) {
    this.router.navigate(['/product', product.id]);
  }

  toggleWishlist(product: Product) {
    const wishlistItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.image,
      colors: product.colors,
      sizes: product.sizes,
      rating: product.rating,
      reviewCount: product.reviewCount,
      isNew: product.isNew,
      onSale: product.onSale,
      category: product.category
    };

    this.wishlistService.toggleWishlist(wishlistItem);
    console.log('Wishlist toggled for:', product.name);
  }

  isInWishlist(productId: number): boolean {
    return this.wishlistService.isInWishlist(productId);
  }
}
