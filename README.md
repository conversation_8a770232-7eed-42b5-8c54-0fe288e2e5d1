# ROOT - Premium Men's T-Shirt Brand Website

A modern, 3D-enhanced, mobile-friendly e-commerce website for ROOT, a premium men's t-shirt brand focused on minimalist design and sustainable fashion.

## 🎯 Project Overview

ROOT is a complete e-commerce website built with Angular 19, featuring:
- **3D Interactive Elements** using Three.js
- **Advanced Animations** with GSAP
- **Mobile-First Responsive Design**
- **Modern UI/UX** with minimalist aesthetics
- **SEO Optimized** with meta tags and accessibility features
- **Performance Optimized** with lazy loading and efficient animations

## 🚀 Features

### 🏠 **Home Page**
- Hero section with 3D rotating T-shirt model
- Scroll-triggered animations and parallax effects
- Categories showcase with 3D hover effects
- Featured products grid with interactive cards
- Brand story section with scroll reveals
- Customer reviews carousel
- Instagram gallery integration
- Newsletter signup with validation

### 🛍️ **Shop Page**
- Advanced filtering system (category, size, color, price)
- Search functionality
- Grid/List view toggle
- Product sorting options
- Responsive product cards with hover effects
- Load more functionality

### ℹ️ **About Page**
- Company story and mission
- Core values showcase
- Team member profiles
- Process timeline
- Impact statistics
- Call-to-action section

### 🛒 **Cart Page**
- Interactive cart management
- Quantity controls
- Price calculations with tax and shipping
- Free shipping progress indicator
- Recommended products
- Responsive design for all devices

### 🧭 **Navigation & Layout**
- Sticky responsive navigation
- Mobile hamburger menu
- Custom cursor effects (desktop)
- Page transition animations
- Footer with comprehensive links

## 🛠️ Tech Stack

- **Framework**: Angular 19 with Standalone Components
- **Styling**: SCSS with modern CSS features
- **3D Graphics**: Three.js for interactive elements
- **Animations**: GSAP with ScrollTrigger
- **Typography**: Inter font family
- **Icons**: Custom SVG icons
- **Build Tool**: Angular CLI
- **Package Manager**: npm

## 📦 Dependencies

```json
{
  "three": "Latest",
  "@types/three": "Latest",
  "gsap": "Latest",
  "swiper": "Latest",
  "@angular/cdk": "^19.0.0"
}
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- Angular CLI

### Installation

1. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

2. **Start development server**
   ```bash
   ng serve
   ```

3. **Open browser**
   Navigate to `http://localhost:4200`

### Building for Production

```bash
ng build --prod
```

## 📱 Responsive Design

The website is built with a mobile-first approach and includes:
- **Mobile**: < 768px - Single column layouts, touch-optimized interactions
- **Tablet**: 768px - 1024px - Adapted grids and navigation
- **Desktop**: > 1024px - Full feature set with advanced interactions

## 🎨 Design System

### Colors
- **Primary**: #000000 (Black)
- **Secondary**: #666666 (Gray)
- **Background**: #f8f9fa (Light Gray)
- **Accent**: Linear gradients (#667eea to #764ba2)

### Typography
- **Font Family**: Inter
- **Headings**: 800-700 weight
- **Body**: 400-500 weight
- **Sizes**: Responsive scale from 0.875rem to 4rem

## 🔧 Development

### Project Structure
```
src/
├── app/
│   ├── features/           # Feature modules
│   │   ├── home/          # Home page
│   │   ├── catalog/       # Shop page
│   │   ├── about/         # About page
│   │   └── cart/          # Cart page
│   ├── shared/            # Shared components & services
│   │   ├── components/    # Reusable components
│   │   ├── services/      # Services
│   │   └── animations/    # Animation configurations
│   └── assets/            # Static assets
```

## 📸 Assets

The project includes a placeholder image generator at:
`http://localhost:4200/assets/images/generate-placeholders.html`

This generates branded placeholder images for all website sections.

## 🎉 Built with ❤️ for the ROOT brand

**Live Demo**: `http://localhost:4200`

---

*This project was generated using [Angular CLI](https://github.com/angular/angular-cli) version 19.2.15.*
