<!-- Hero Section -->
<section class="hero-section">
  <div class="hero-content">
    <div class="hero-text">
      <h1 class="hero-title">
        <span class="title-line">Elevate Your</span>
        <span class="title-line highlight">Basics</span>
        <span class="title-line">with ROOT</span>
      </h1>
      <p class="hero-subtitle">Premium Men's T-Shirts | Minimal. Modern. Masculine.</p>
      <div class="hero-buttons">
        <button class="btn btn-primary" routerLink="/shop">Shop Now</button>
        <button class="btn btn-secondary" (click)="scrollToSection('categories')">See Collection</button>
      </div>
    </div>
    <div class="hero-3d">
      <div #threeContainer class="three-container"></div>
    </div>
  </div>
  <div class="scroll-indicator" (click)="scrollToSection('categories')">
    <div class="scroll-text">Scroll</div>
    <div class="scroll-arrow">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M7 13L12 18L17 13"></path>
        <path d="M7 6L12 11L17 6"></path>
      </svg>
    </div>
  </div>
</section>

<!-- Top Categories Section -->
<section id="categories" class="categories-section">
  <div class="container">
    <h2 class="section-title">Explore by Style</h2>
    <div class="categories-scroll">
      <div class="category-card" *ngFor="let category of categories"
           (click)="navigateToCategory(category.slug)"
           [style.background-image]="'url(' + category.image + ')'">
        <div class="category-overlay">
          <h3 class="category-title">{{ category.name }}</h3>
          <p class="category-count">{{ category.count }} items</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Featured Products Section -->
<section class="featured-section">
  <div class="container">
    <h2 class="section-title">Trending Now</h2>
    <div class="products-grid">
      <div class="product-card" *ngFor="let product of featuredProducts">
        <div class="product-image">
          <img [src]="product.image" [alt]="product.name" loading="lazy">
          <div class="product-overlay">
            <button class="quick-add-btn" (click)="addToCart(product)">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 5V19M5 12H19"></path>
              </svg>
              Quick Add
            </button>
          </div>
        </div>
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <p class="product-price">${{ product.price }}</p>
        </div>
      </div>
    </div>
    <div class="section-footer">
      <button class="btn btn-outline" routerLink="/shop">View All Products</button>
    </div>
  </div>
</section>

<!-- Brand Story Section -->
<section class="brand-story-section">
  <div class="container">
    <div class="story-content">
      <div class="story-image">
        <img src="assets/images/brand-story.jpg" alt="ROOT Brand Story" loading="lazy">
      </div>
      <div class="story-text">
        <h2 class="section-title">What ROOT Stands For</h2>
        <div class="story-points">
          <div class="story-point">
            <div class="point-icon">🌱</div>
            <div class="point-content">
              <h3>Premium Organic Cotton</h3>
              <p>Sourced from sustainable farms, our cotton is soft, breathable, and built to last.</p>
            </div>
          </div>
          <div class="story-point">
            <div class="point-icon">💪</div>
            <div class="point-content">
              <h3>Designed for Confidence</h3>
              <p>Every cut, every stitch is crafted to make you look and feel your absolute best.</p>
            </div>
          </div>
          <div class="story-point">
            <div class="point-icon">🤝</div>
            <div class="point-content">
              <h3>Ethically Sourced & Crafted</h3>
              <p>Fair wages, safe working conditions, and environmental responsibility guide everything we do.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Customer Reviews Section -->
<section class="reviews-section">
  <div class="container">
    <h2 class="section-title">What Our Customers Say</h2>
    <div class="reviews-carousel" #reviewsCarousel>
      <div class="review-card" *ngFor="let review of customerReviews">
        <div class="review-content">
          <div class="review-stars">
            <span *ngFor="let star of [1,2,3,4,5]" class="star" [class.filled]="star <= review.rating">★</span>
          </div>
          <p class="review-text">"{{ review.text }}"</p>
          <div class="review-author">
            <img [src]="review.avatar" [alt]="review.name" class="author-avatar">
            <div class="author-info">
              <h4 class="author-name">{{ review.name }}</h4>
              <p class="author-title">{{ review.title }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Instagram Gallery Section -->
<section class="instagram-section">
  <div class="container">
    <h2 class="section-title">#WearROOT</h2>
    <div class="instagram-grid">
      <div class="instagram-item" *ngFor="let post of instagramPosts"
           (click)="openInstagramPost(post.url)">
        <img [src]="post.image" [alt]="'Instagram post ' + post.id" loading="lazy">
        <div class="instagram-overlay">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section">
  <div class="container">
    <div class="newsletter-content">
      <h2 class="newsletter-title">Get 10% Off Your First Order</h2>
      <p class="newsletter-subtitle">Join our community and be the first to know about new drops, exclusive offers, and style tips.</p>
      <form class="newsletter-form" (ngSubmit)="subscribeNewsletter()" #newsletterForm="ngForm">
        <div class="form-group">
          <input
            type="email"
            class="newsletter-input"
            placeholder="Enter your email address"
            [(ngModel)]="newsletterEmail"
            name="email"
            required
            email
            #emailInput="ngModel">
          <button type="submit" class="newsletter-btn" [disabled]="!newsletterForm.valid">
            Subscribe
          </button>
        </div>
        <div class="form-validation" *ngIf="emailInput.invalid && emailInput.touched">
          <p *ngIf="emailInput.errors?.['required']">Email is required</p>
          <p *ngIf="emailInput.errors?.['email']">Please enter a valid email</p>
        </div>
      </form>
    </div>
  </div>
</section>
