import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { WishlistService, WishlistItem } from '../../shared/services/wishlist.service';
import { CartService } from '../../shared/services/cart.service';
import { MetaService } from '../../shared/services/meta.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-wishlist',
  imports: [CommonModule, RouterModule],
  templateUrl: './wishlist.component.html',
  styleUrl: './wishlist.component.scss'
})
export class WishlistComponent implements OnInit, OnDestroy {
  wishlistItems: WishlistItem[] = [];
  private wishlistSubscription?: Subscription;

  constructor(
    private wishlistService: WishlistService,
    private cartService: CartService,
    private metaService: MetaService,
    private router: Router
  ) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'My Wishlist',
      description: 'View and manage your saved ROOT products. Add your favorite items to cart or remove them from your wishlist.',
      keywords: 'wishlist, saved items, ROOT favorites, shopping list',
      url: window.location.href
    });

    // Subscribe to wishlist changes
    this.wishlistSubscription = this.wishlistService.wishlist$.subscribe(items => {
      this.wishlistItems = items;
    });
  }

  ngOnDestroy() {
    if (this.wishlistSubscription) {
      this.wishlistSubscription.unsubscribe();
    }
  }

  trackByItem(index: number, item: WishlistItem): number {
    return item.id;
  }

  viewProduct(item: WishlistItem) {
    this.router.navigate(['/product', item.id]);
  }

  removeFromWishlist(item: WishlistItem) {
    this.wishlistService.removeFromWishlist(item.id);
  }

  addToCart(item: WishlistItem) {
    const cartItem = {
      productId: item.id,
      name: item.name,
      price: item.price,
      image: item.image,
      size: item.sizes[0], // Default to first available size
      color: item.colors[0].name, // Default to first available color
      quantity: 1
    };

    this.cartService.addToCart(cartItem);
    console.log('Added to cart from wishlist:', cartItem);
  }

  clearWishlist() {
    if (confirm('Are you sure you want to clear your entire wishlist?')) {
      this.wishlistService.clearWishlist();
    }
  }

  addAllToCart() {
    this.wishlistItems.forEach(item => {
      this.addToCart(item);
    });
    console.log('Added all wishlist items to cart');
  }
}
