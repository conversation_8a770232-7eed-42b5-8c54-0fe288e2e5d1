<section class="auth-section">
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1>Join <PERSON></h1>
        <p>Create your account and discover premium essentials</p>
      </div>

      <form class="auth-form" (ngSubmit)="onSubmit()" #signupForm="ngForm">
        <div class="form-row">
          <div class="form-group">
            <label for="firstName">First Name</label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              [(ngModel)]="formData.firstName"
              #firstNameInput="ngModel"
              required
              class="form-input"
              [class.error]="firstNameInput.invalid && firstNameInput.touched"
              placeholder="Enter your first name">
            <div class="form-error" *ngIf="firstNameInput.invalid && firstNameInput.touched">
              <span *ngIf="firstNameInput.errors?.['required']">First name is required</span>
            </div>
          </div>

          <div class="form-group">
            <label for="lastName">Last Name</label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              [(ngModel)]="formData.lastName"
              #lastNameInput="ngModel"
              required
              class="form-input"
              [class.error]="lastNameInput.invalid && lastNameInput.touched"
              placeholder="Enter your last name">
            <div class="form-error" *ngIf="lastNameInput.invalid && lastNameInput.touched">
              <span *ngIf="lastNameInput.errors?.['required']">Last name is required</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email Address</label>
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="formData.email"
            #emailInput="ngModel"
            required
            email
            class="form-input"
            [class.error]="emailInput.invalid && emailInput.touched"
            placeholder="Enter your email">
          <div class="form-error" *ngIf="emailInput.invalid && emailInput.touched">
            <span *ngIf="emailInput.errors?.['required']">Email is required</span>
            <span *ngIf="emailInput.errors?.['email']">Please enter a valid email</span>
          </div>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <div class="password-input">
            <input
              [type]="showPassword ? 'text' : 'password'"
              id="password"
              name="password"
              [(ngModel)]="formData.password"
              #passwordInput="ngModel"
              required
              minlength="8"
              pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
              class="form-input"
              [class.error]="passwordInput.invalid && passwordInput.touched"
              placeholder="Create a strong password">
            <button
              type="button"
              class="password-toggle"
              (click)="togglePassword()"
              aria-label="Toggle password visibility">
              <svg *ngIf="!showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              <svg *ngIf="showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
            </button>
          </div>
          <div class="password-strength">
            <div class="strength-bar">
              <div class="strength-fill" [style.width.%]="passwordStrength" [class]="getPasswordStrengthClass()"></div>
            </div>
            <span class="strength-text">{{ getPasswordStrengthText() }}</span>
          </div>
          <div class="form-error" *ngIf="passwordInput.invalid && passwordInput.touched">
            <span *ngIf="passwordInput.errors?.['required']">Password is required</span>
            <span *ngIf="passwordInput.errors?.['minlength']">Password must be at least 8 characters</span>
            <span *ngIf="passwordInput.errors?.['pattern']">Password must contain uppercase, lowercase, number and special character</span>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            [(ngModel)]="formData.confirmPassword"
            #confirmPasswordInput="ngModel"
            required
            class="form-input"
            [class.error]="confirmPasswordInput.invalid && confirmPasswordInput.touched || passwordMismatch"
            placeholder="Confirm your password">
          <div class="form-error" *ngIf="(confirmPasswordInput.invalid && confirmPasswordInput.touched) || passwordMismatch">
            <span *ngIf="confirmPasswordInput.errors?.['required']">Please confirm your password</span>
            <span *ngIf="passwordMismatch">Passwords do not match</span>
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" [(ngModel)]="acceptTerms" name="acceptTerms" required>
            <span class="checkmark"></span>
            I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
          </label>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" [(ngModel)]="subscribeNewsletter" name="subscribeNewsletter">
            <span class="checkmark"></span>
            Subscribe to our newsletter for exclusive offers and updates
          </label>
        </div>

        <div class="form-error" *ngIf="signupError">
          {{ signupError }}
        </div>

        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="!signupForm.valid || passwordMismatch || isLoading">
          <span *ngIf="!isLoading">Create Account</span>
          <span *ngIf="isLoading" class="loading-spinner">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
            Creating account...
          </span>
        </button>
      </form>

      <div class="auth-divider">
        <span>or</span>
      </div>

      <div class="social-login">
        <button class="btn btn-social google" (click)="signupWithGoogle()">
          <svg width="20" height="20" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Continue with Google
        </button>

        <button class="btn btn-social facebook" (click)="signupWithFacebook()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="#1877F2">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
          Continue with Facebook
        </button>
      </div>

      <div class="auth-footer">
        <p>Already have an account? <a routerLink="/login">Sign in</a></p>
      </div>
    </div>
  </div>
</section>
