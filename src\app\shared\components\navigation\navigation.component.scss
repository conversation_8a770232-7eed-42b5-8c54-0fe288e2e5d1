.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  &.scrolled {
    padding: 0.5rem 0;
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  }
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-logo {
  .logo-link {
    text-decoration: none;
    color: #000;

    .logo-text {
      font-size: 1.75rem;
      font-weight: 800;
      letter-spacing: -0.02em;
      color: #000;
      transition: color 0.3s ease;

      &:hover {
        color: #333;
      }
    }
  }
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;

  .nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: #000;
    }

    &.active {
      color: #000;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        right: 0;
        height: 2px;
        background: #000;
        border-radius: 1px;
      }
    }
  }
}

.nav-icons {
  display: flex;
  align-items: center;
  gap: 1rem;

  .icon-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    color: #333;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
      color: #000;
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .cart-btn {
    .cart-count {
      position: absolute;
      top: -2px;
      right: -2px;
      background: #000;
      color: white;
      font-size: 0.75rem;
      font-weight: 600;
      padding: 0.125rem 0.375rem;
      border-radius: 10px;
      min-width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.hamburger {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 4px;

  .hamburger-line {
    width: 24px;
    height: 2px;
    background: #000;
    transition: all 0.3s ease;
    border-radius: 1px;
  }

  &.active {
    .hamburger-line:nth-child(1) {
      transform: rotate(45deg) translate(6px, 6px);
    }

    .hamburger-line:nth-child(2) {
      opacity: 0;
    }

    .hamburger-line:nth-child(3) {
      transform: rotate(-45deg) translate(6px, -6px);
    }
  }
}

.mobile-menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Mobile Styles
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 280px;
    height: 100vh;
    background: white;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 6rem 2rem 2rem;
    gap: 2rem;
    transition: right 0.3s ease;
    z-index: 1001;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);

    &.active {
      right: 0;
    }

    .nav-link {
      font-size: 1.1rem;
      padding: 0.5rem 0;
      width: 100%;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      &.active::after {
        display: none;
      }
    }
  }

  .hamburger {
    display: flex;
    z-index: 1002;
  }

  .mobile-menu-overlay {
    display: block;
  }

  .nav-icons {
    gap: 0.5rem;

    .icon-btn {
      padding: 0.375rem;

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}

// User Menu
.user-menu {
  position: relative;
}

.user-dropdown {
  position: absolute;
  top: calc(100% + 1rem);
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  min-width: 220px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.user-info {
  padding: 1rem;
  border-bottom: 1px solid #eee;

  .user-name {
    display: block;
    font-weight: 600;
    color: #000;
    font-size: 0.95rem;
  }

  .user-email {
    display: block;
    color: #666;
    font-size: 0.85rem;
    margin-top: 0.25rem;
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  cursor: pointer;

  &:hover {
    background: #f8f9fa;
    color: #000;
  }

  &.logout-btn {
    color: #dc3545;

    &:hover {
      background: rgba(220, 53, 69, 0.1);
      color: #dc3545;
    }
  }

  svg {
    flex-shrink: 0;
  }
}

.dropdown-divider {
  height: 1px;
  background: #eee;
  margin: 0.5rem 0;
}

// Auth Buttons
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.auth-btn {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;

  &.login-btn {
    color: #666;

    &:hover {
      color: #000;
    }
  }

  &.signup-btn {
    background: #000;
    color: white;

    &:hover {
      background: #333;
      transform: translateY(-1px);
    }
  }
}

// Mobile auth buttons
@media (max-width: 768px) {
  .auth-buttons {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
  }

  .auth-btn {
    width: 100%;
    text-align: center;
    padding: 0.75rem 1rem;

    &.signup-btn {
      order: -1;
    }
  }

  .user-dropdown {
    right: -1rem;
    left: -1rem;
    width: auto;
  }
}
