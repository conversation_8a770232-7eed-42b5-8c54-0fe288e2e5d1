// Import shared auth styles
@import '../login/login.component.scss';

// Signup-specific styles
.auth-container {
  max-width: 520px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.password-strength {
  margin-top: 0.75rem;

  .strength-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    backdrop-filter: blur(10px);
  }

  .strength-fill {
    height: 100%;
    border-radius: 3px;
    transition: all 0.5s ease;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: shimmer 2s infinite;
    }

    &.weak {
      background: linear-gradient(90deg, #dc3545, #e74c3c);
    }

    &.fair {
      background: linear-gradient(90deg, #ffc107, #f39c12);
    }

    &.good {
      background: linear-gradient(90deg, #28a745, #2ecc71);
    }

    &.strong {
      background: linear-gradient(90deg, #667eea, #764ba2);
    }
  }

  .strength-text {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.weak {
      color: #dc3545;
    }

    &.fair {
      color: #ffc107;
    }

    &.good {
      color: #28a745;
    }

    &.strong {
      background: linear-gradient(90deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

// Responsive adjustments for signup
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .auth-container {
    max-width: 100%;
  }
}
