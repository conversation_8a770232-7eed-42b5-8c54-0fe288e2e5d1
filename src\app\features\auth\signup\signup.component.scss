// Import shared auth styles
@use '../login/login.component.scss';

// Signup-specific styles
.auth-container {
  max-width: 500px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.password-strength {
  margin-top: 0.5rem;

  .strength-bar {
    width: 100%;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
  }

  .strength-fill {
    height: 100%;
    border-radius: 2px;
    transition: all 0.3s ease;

    &.weak {
      background: #dc3545;
    }

    &.fair {
      background: #ffc107;
    }

    &.good {
      background: #28a745;
    }

    &.strong {
      background: #17a2b8;
    }
  }

  .strength-text {
    font-size: 0.875rem;
    font-weight: 500;

    &.weak {
      color: #dc3545;
    }

    &.fair {
      color: #ffc107;
    }

    &.good {
      color: #28a745;
    }

    &.strong {
      color: #17a2b8;
    }
  }
}

// Responsive adjustments for signup
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .auth-container {
    max-width: 100%;
  }
}
