import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface WishlistItem {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  colors: Array<{name: string; value: string; hex: string}>;
  sizes: string[];
  rating: number;
  reviewCount: number;
  isNew?: boolean;
  onSale?: boolean;
  category: string;
}

@Injectable({
  providedIn: 'root'
})
export class WishlistService {
  private wishlistItems: WishlistItem[] = [];
  private wishlistSubject = new BehaviorSubject<WishlistItem[]>([]);

  wishlist$ = this.wishlistSubject.asObservable();

  constructor() {
    // Load wishlist from localStorage on initialization
    this.loadWishlistFromStorage();
  }

  private loadWishlistFromStorage() {
    const savedWishlist = localStorage.getItem('wishlist');
    if (savedWishlist) {
      this.wishlistItems = JSON.parse(savedWishlist);
      this.wishlistSubject.next(this.wishlistItems);
    }
  }

  private saveWishlistToStorage() {
    localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));
    this.wishlistSubject.next(this.wishlistItems);
  }

  addToWishlist(item: WishlistItem) {
    const existingItem = this.wishlistItems.find(wishlistItem => wishlistItem.id === item.id);

    if (!existingItem) {
      this.wishlistItems.push(item);
      this.saveWishlistToStorage();
    }
  }

  removeFromWishlist(itemId: number) {
    this.wishlistItems = this.wishlistItems.filter(item => item.id !== itemId);
    this.saveWishlistToStorage();
  }

  isInWishlist(itemId: number): boolean {
    return this.wishlistItems.some(item => item.id === itemId);
  }

  toggleWishlist(item: WishlistItem) {
    if (this.isInWishlist(item.id)) {
      this.removeFromWishlist(item.id);
    } else {
      this.addToWishlist(item);
    }
  }

  getWishlistItems(): WishlistItem[] {
    return [...this.wishlistItems];
  }

  getWishlistCount(): number {
    return this.wishlistItems.length;
  }

  clearWishlist() {
    this.wishlistItems = [];
    this.saveWishlistToStorage();
  }
}
