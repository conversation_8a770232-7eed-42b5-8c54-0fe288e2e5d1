import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CartService, CartItem } from '../../shared/services/cart.service';
import { MetaService } from '../../shared/services/meta.service';

interface ShippingInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

interface PaymentInfo {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardName: string;
}

interface ShippingOption {
  id: string;
  name: string;
  price: number;
  time: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  icons: string[];
}

@Component({
  selector: 'app-checkout',
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './checkout.component.html',
  styleUrl: './checkout.component.scss'
})
export class CheckoutComponent implements OnInit {
  currentStep = 1;
  cartItems: CartItem[] = [];
  subtotal = 0;
  shippingCost = 0;
  tax = 0;
  total = 0;
  promoCode = '';
  isProcessing = false;

  // Shipping information
  shippingInfo: ShippingInfo = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US'
  };

  // Shipping options
  shippingOptions: ShippingOption[] = [
    { id: 'standard', name: 'Standard Shipping', price: 5.99, time: '5-7 business days' },
    { id: 'express', name: 'Express Shipping', price: 12.99, time: '2-3 business days' },
    { id: 'overnight', name: 'Overnight Shipping', price: 24.99, time: '1 business day' }
  ];
  selectedShippingMethod = 'standard';

  // Payment information
  paymentInfo: PaymentInfo = {
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: ''
  };

  // Payment methods
  paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Credit / Debit Card',
      icons: [
        'assets/images/visa.svg',
        'assets/images/mastercard.svg',
        'assets/images/amex.svg'
      ]
    },
    {
      id: 'paypal',
      name: 'PayPal',
      icons: ['assets/images/paypal.svg']
    }
  ];
  selectedPaymentMethod = 'card';
  sameAsShipping = true;

  constructor(
    private cartService: CartService,
    private router: Router,
    private metaService: MetaService
  ) {}

  ngOnInit() {
    this.metaService.updatePageMeta({
      title: 'Checkout',
      description: 'Complete your purchase securely. Review your cart, shipping, and payment details.',
      keywords: 'checkout, payment, shipping, order, ROOT',
      url: window.location.href
    });

    // Get cart items
    this.cartItems = this.cartService.getCartItems();

    // Calculate totals
    this.calculateTotals();

    // Redirect to cart if cart is empty
    if (this.cartItems.length === 0) {
      this.router.navigate(['/cart']);
    }
  }

  calculateTotals() {
    this.subtotal = this.cartService.getCartTotal();

    // Calculate shipping cost based on selected method
    const selectedShipping = this.shippingOptions.find(option => option.id === this.selectedShippingMethod);
    this.shippingCost = selectedShipping ? selectedShipping.price : 0;

    // Calculate tax (8.5% for demo)
    this.tax = (this.subtotal + this.shippingCost) * 0.085;

    // Calculate total
    this.total = this.subtotal + this.shippingCost + this.tax;
  }

  nextStep() {
    if (this.currentStep < 3) {
      this.currentStep++;
      this.calculateTotals();
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  goToStep(step: number) {
    this.currentStep = step;
  }

  applyPromoCode() {
    // Mock promo code logic
    if (this.promoCode.toLowerCase() === 'save10') {
      this.subtotal = this.subtotal * 0.9; // 10% discount
      this.calculateTotals();
      console.log('Promo code applied: 10% discount');
    } else if (this.promoCode.toLowerCase() === 'freeship') {
      this.shippingCost = 0;
      this.calculateTotals();
      console.log('Promo code applied: Free shipping');
    } else {
      console.log('Invalid promo code');
    }
  }

  placeOrder() {
    this.isProcessing = true;

    // Mock order processing
    setTimeout(() => {
      this.isProcessing = false;

      // Clear cart
      this.cartService.clearCart();

      // Navigate to order confirmation
      this.router.navigate(['/order-confirmation'], {
        queryParams: {
          orderNumber: this.generateOrderNumber(),
          total: this.total.toFixed(2)
        }
      });
    }, 3000);
  }

  private generateOrderNumber(): string {
    return 'ROOT-' + Date.now().toString().slice(-8);
  }
}
