// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #000;
  letter-spacing: -0.02em;
}

// Hero Section
.about-hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 2rem;
  color: #000;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

// Story Section
.story-section {
  padding: 6rem 0;
  background: white;
}

.story-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.story-text {
  .section-title {
    text-align: left;
    margin-bottom: 2rem;
  }

  p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.story-image {
  img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 15px;
  }
}

// Values Section
.values-section {
  padding: 6rem 0;
  background: #f8f9fa;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.value-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.value-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.value-card p {
  color: #666;
  line-height: 1.6;
}

// Team Section
.team-section {
  padding: 6rem 0;
  background: white;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 3rem;
}

.team-member {
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);

    .member-image img {
      transform: scale(1.05);
    }
  }
}

.member-image {
  width: 200px;
  height: 200px;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.member-info {
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #000;
  }

  .member-role {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
    margin-bottom: 1rem;
  }

  .member-bio {
    color: #666;
    line-height: 1.6;
  }
}

// Process Section
.process-section {
  padding: 6rem 0;
  background: #f8f9fa;
}

.process-timeline {
  max-width: 800px;
  margin: 0 auto;
}

.process-step {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  margin-bottom: 3rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.step-number {
  width: 60px;
  height: 60px;
  background: #000;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  padding-top: 0.5rem;

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #000;
  }

  p {
    color: #666;
    line-height: 1.6;
    font-size: 1.1rem;
  }
}

// Impact Section
.impact-section {
  padding: 6rem 0;
  background: white;
}

.impact-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.stat-card {
  text-align: center;
  padding: 2rem;
  border-radius: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  }
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.stat-description {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

// CTA Section
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: white;
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;

  h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.6;
  }
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;

  .btn {
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 160px;

    &.btn-primary {
      background: white;
      color: #000;
      border: 2px solid white;

      &:hover {
        background: transparent;
        color: white;
        transform: translateY(-2px);
      }
    }

    &.btn-secondary {
      background: transparent;
      color: white;
      border: 2px solid white;

      &:hover {
        background: white;
        color: #000;
        transform: translateY(-2px);
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .story-grid {
    gap: 2rem;
  }

  .process-step {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .story-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .story-image {
    order: -1;

    img {
      height: 300px;
    }
  }

  .values-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .value-card {
    padding: 2rem;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .process-step {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .step-number {
    margin: 0 auto;
  }

  .impact-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;

    .btn {
      width: 100%;
      max-width: 300px;
    }
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .value-card {
    padding: 1.5rem;
  }

  .member-image {
    width: 150px;
    height: 150px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .cta-content h2 {
    font-size: 1.75rem;
  }
}
