// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

// Cart Header
.cart-header {
  padding: 8rem 0 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #000;
}

.cart-count {
  font-size: 1.1rem;
  color: #666;
}

// Cart Content
.cart-content {
  padding: 3rem 0;
  background: white;
  min-height: 60vh;
}

// Empty Cart
.empty-cart {
  text-align: center;
  padding: 4rem 2rem;
  max-width: 500px;
  margin: 0 auto;
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: 2rem;
  opacity: 0.5;
}

.empty-cart h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.empty-cart p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &.btn-primary {
    background: #000;
    color: white;

    &:hover {
      background: #333;
      transform: translateY(-2px);
    }
  }

  &.btn-secondary {
    background: transparent;
    color: #000;
    border: 2px solid #000;

    &:hover {
      background: #000;
      color: white;
      transform: translateY(-2px);
    }
  }
}

// Cart Layout
.cart-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

// Cart Items
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cart-item {
  display: grid;
  grid-template-columns: 120px 1fr auto auto auto;
  gap: 1.5rem;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  }
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 10px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.item-options {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.item-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: #000;
}

.item-quantity {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 2px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  background: white;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: #f8f9fa;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.quantity-input {
  width: 60px;
  height: 40px;
  border: none;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  outline: none;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
}

.item-total {
  font-size: 1.25rem;
  font-weight: 700;
  color: #000;
  text-align: right;
}

.item-actions {
  display: flex;
  justify-content: center;
}

.remove-btn {
  width: 40px;
  height: 40px;
  background: none;
  border: 2px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;

  &:hover {
    border-color: #dc3545;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
  }
}

// Cart Summary
.cart-summary {
  position: sticky;
  top: 100px;
}

.summary-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 15px;

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #000;
  }
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;

  &:last-of-type {
    border-bottom: none;
  }

  &.total {
    font-size: 1.25rem;
    font-weight: 700;
    color: #000;
    border-top: 2px solid #000;
    margin-top: 1rem;
    padding-top: 1rem;
  }

  span:first-child {
    color: #666;
  }

  span:last-child {
    font-weight: 600;
    color: #000;
  }
}

.free-shipping-notice {
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(40, 167, 69, 0.1);
  border-radius: 8px;
  text-align: center;

  p {
    margin: 0 0 0.5rem;
    color: #28a745;
    font-weight: 500;
    font-size: 0.9rem;
  }
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #eee;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #28a745;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.checkout-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;

  .checkout-btn {
    width: 100%;
    padding: 1.25rem;
    font-size: 1.1rem;
  }
}

.payment-methods {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;

  p {
    margin: 0 0 0.5rem;
    color: #666;
    font-size: 0.9rem;
  }
}

.payment-icons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;

  .payment-icon {
    font-size: 1.5rem;
  }
}

// Recommended Section
.recommended-section {
  padding: 4rem 0;
  background: #f8f9fa;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  color: #000;
}

.recommended-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);

    .product-overlay {
      opacity: 1;
      visibility: visible;
    }

    .product-image img {
      transform: scale(1.05);
    }
  }
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.quick-add-btn {
  padding: 0.75rem 1.5rem;
  background: white;
  color: #000;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #f8f9fa;
    transform: scale(1.05);
  }
}

.product-info {
  padding: 1.5rem;
  text-align: center;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #000;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #000;
}

// Screen reader only
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Responsive Design
@media (max-width: 1024px) {
  .cart-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .cart-summary {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
    gap: 1rem;
    padding: 1rem;

    .item-image {
      width: 80px;
      height: 80px;
    }

    .item-details {
      grid-column: 2;
    }

    .item-quantity {
      grid-column: 1 / -1;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .item-total {
      text-align: left;
    }

    .item-actions {
      justify-content: flex-end;
    }
  }

  .summary-card {
    padding: 1.5rem;
  }

  .recommended-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .product-image {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }

  .cart-item {
    grid-template-columns: 1fr;
    text-align: center;

    .item-image {
      width: 120px;
      height: 120px;
      margin: 0 auto;
    }

    .item-details {
      grid-column: 1;
    }

    .item-quantity {
      grid-column: 1;
      justify-content: center;
      gap: 1rem;
    }

    .item-total {
      text-align: center;
      font-size: 1.5rem;
    }

    .item-actions {
      justify-content: center;
    }
  }

  .checkout-actions {
    .btn {
      padding: 1rem;
      font-size: 1rem;
    }
  }

  .recommended-grid {
    grid-template-columns: 1fr;
  }
}
