// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

// Breadcrumb
.breadcrumb-section {
  padding: 6rem 0 2rem;
  background: #f8f9fa;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;

  a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: #000;
    }
  }

  .separator {
    color: #ccc;
  }

  .current {
    color: #000;
    font-weight: 500;
  }
}

// Checkout Section
.checkout-section {
  padding: 3rem 0;
  background: white;
  min-height: 80vh;
}

.checkout-header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 2rem;
  }
}

// Checkout Stepper
.checkout-stepper {
  margin-bottom: 3rem;
}

.stepper-container {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;

  .step-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    border: 3px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    position: relative;

    .step-number {
      font-weight: 700;
      font-size: 1.1rem;
      color: #6c757d;
    }

    svg {
      color: white;
    }
  }

  .step-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    .step-title {
      font-weight: 600;
      font-size: 1rem;
      color: #6c757d;
      transition: color 0.3s ease;
    }

    .step-description {
      font-size: 0.875rem;
      color: #adb5bd;
      transition: color 0.3s ease;
    }
  }

  &.active {
    .step-circle {
      background: #000;
      border-color: #000;
      transform: scale(1.1);

      .step-number {
        color: white;
      }
    }

    .step-content {
      .step-title {
        color: #000;
      }

      .step-description {
        color: #6c757d;
      }
    }
  }

  &.completed {
    .step-circle {
      background: #28a745;
      border-color: #28a745;

      .step-number {
        color: white;
      }
    }

    .step-content {
      .step-title {
        color: #28a745;
      }

      .step-description {
        color: #6c757d;
      }
    }
  }
}

.step-connector {
  flex: 1;
  height: 3px;
  background: #e9ecef;
  margin: 0 1rem;
  position: relative;
  top: -25px;
  transition: all 0.3s ease;

  &.completed {
    background: #28a745;
  }
}

// Checkout Content
.checkout-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

// Checkout Forms
.checkout-forms {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 2rem;
}

.checkout-step {
  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #000;
  }
}

.checkout-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 600;
    color: #000;
    font-size: 0.95rem;
  }
}

.form-input {
  padding: 1rem 1.25rem;
  border: 2px solid #eee;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
  background: white;

  &:focus {
    border-color: #000;
    transform: translateY(-1px);
  }

  &::placeholder {
    color: #999;
  }
}

// Shipping Options
.shipping-options {
  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #000;
  }
}

.shipping-option {
  margin-bottom: 1rem;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #ccc;
  }

  input[type="radio"] {
    display: none;

    &:checked + .radio-custom {
      background: #000;
      border-color: #000;

      &::after {
        opacity: 1;
      }
    }

    &:checked ~ * {
      color: #000;
    }
  }

  .radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 8px;
      background: white;
      border-radius: 50%;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  .option-details {
    flex: 1;

    .option-name {
      display: block;
      font-weight: 600;
      color: #000;
    }

    .option-time {
      display: block;
      font-size: 0.9rem;
      color: #666;
    }
  }

  .option-price {
    font-weight: 600;
    color: #000;
  }
}

// Payment Methods
.payment-methods {
  margin-bottom: 2rem;
}

.payment-method {
  margin-bottom: 1rem;

  .radio-label {
    justify-content: space-between;

    .method-name {
      font-weight: 600;
      color: #000;
    }

    .method-icons {
      display: flex;
      gap: 0.5rem;

      .payment-icon {
        height: 24px;
        width: auto;
      }
    }
  }
}

// Card Details
.card-details {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 2px solid #eee;
  margin-top: 1rem;
}

// Checkbox
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.95rem;
  color: #666;

  input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 2px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  input:checked + .checkmark {
    background: #000;
    border-color: #000;

    &::after {
      opacity: 1;
    }
  }
}

// Form Actions
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;

  .btn {
    flex: 1;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.btn-primary {
      background: #000;
      color: white;

      &:hover:not(:disabled) {
        background: #333;
        transform: translateY(-2px);
      }
    }

    &.btn-secondary {
      background: transparent;
      color: #000;
      border: 2px solid #000;

      &:hover:not(:disabled) {
        background: #000;
        color: white;
        transform: translateY(-2px);
      }
    }
  }
}

// Order Review
.order-review {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.review-section {
  position: relative;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 2px solid #eee;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #000;
  }

  .review-details {
    color: #666;
    line-height: 1.6;

    p {
      margin: 0.25rem 0;
    }
  }

  .btn-edit {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: #666;
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.9rem;

    &:hover {
      color: #000;
    }
  }
}

// Order Summary
.order-summary {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 2rem;
  height: fit-content;
  position: sticky;
  top: 2rem;

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #000;
  }
}

.summary-items {
  margin-bottom: 2rem;
}

.summary-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  .item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .item-details {
    flex: 1;

    h4 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
      color: #000;
    }

    p {
      font-size: 0.9rem;
      color: #666;
      margin: 0.125rem 0;
    }
  }

  .item-price {
    font-weight: 600;
    color: #000;
  }
}

.summary-totals {
  padding: 1.5rem 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  margin-bottom: 1.5rem;
}

.total-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;

  &:last-child {
    margin-bottom: 0;
  }

  &.total {
    font-size: 1.25rem;
    font-weight: 700;
    color: #000;
    padding-top: 0.75rem;
    border-top: 1px solid #eee;
  }
}

.promo-code {
  display: flex;
  gap: 0.5rem;

  .promo-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 0.9rem;
    outline: none;

    &:focus {
      border-color: #000;
    }
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

// Loading Spinner
.loading-spinner {
  svg {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 1024px) {
  .checkout-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .order-summary {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .breadcrumb-section {
    padding: 5rem 0 1rem;
  }

  .checkout-header h1 {
    font-size: 2rem;
  }

  .stepper-container {
    flex-direction: column;
    gap: 0;
    max-width: 100%;

    .step {
      flex-direction: row;
      text-align: left;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 12px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

      .step-circle {
        margin-bottom: 0;
        margin-right: 1rem;
        width: 45px;
        height: 45px;
        flex-shrink: 0;
      }

      .step-content {
        align-items: flex-start;
        flex: 1;

        .step-title {
          font-size: 1.1rem;
          margin-bottom: 0.25rem;
        }

        .step-description {
          font-size: 0.9rem;
        }
      }

      &.active {
        background: rgba(0, 0, 0, 0.05);
        border: 2px solid #000;
      }

      &.completed {
        background: rgba(40, 167, 69, 0.1);
        border: 2px solid #28a745;
      }
    }

    .step-connector {
      display: none;
    }
  }

  .checkout-forms {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .order-summary {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .checkout-header {
    margin-bottom: 2rem;

    h1 {
      font-size: 1.75rem;
      margin-bottom: 1.5rem;
    }
  }

  .stepper-container {
    .step {
      padding: 0.75rem;
      margin-bottom: 0.75rem;

      .step-circle {
        width: 40px;
        height: 40px;
        margin-right: 0.75rem;
      }

      .step-content {
        .step-title {
          font-size: 1rem;
        }

        .step-description {
          font-size: 0.85rem;
        }
      }
    }
  }

  .checkout-forms {
    padding: 1rem;
    border-radius: 12px;
  }

  .checkout-step h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-input {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }

  .order-summary {
    padding: 1rem;
    border-radius: 12px;
  }
}
