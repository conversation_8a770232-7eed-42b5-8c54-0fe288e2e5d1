import { trigger, transition, style, query, animateChild, group, animate } from '@angular/animations';

export const slideInAnimation = trigger('routeAnimations', [
  transition('* <=> *', [
    // Set a default style for enter and leave
    query(':enter, :leave', [
      style({
        position: 'absolute',
        left: 0,
        width: '100%',
        opacity: 0,
        transform: 'scale(0.8)',
      }),
    ], { optional: true }),
    
    // Animate the new page in
    query(':enter', [
      animate('600ms ease', style({ opacity: 1, transform: 'scale(1)' })),
    ], { optional: true })
  ]),
]);

export const fadeAnimation = trigger('fadeAnimation', [
  transition('* <=> *', [
    query(':enter, :leave', [
      style({
        position: 'absolute',
        left: 0,
        width: '100%',
        opacity: 0,
      }),
    ], { optional: true }),
    
    query(':enter', [
      animate('400ms ease-in', style({ opacity: 1 })),
    ], { optional: true })
  ]),
]);

export const slideUpAnimation = trigger('slideUpAnimation', [
  transition('* <=> *', [
    query(':enter, :leave', [
      style({
        position: 'absolute',
        left: 0,
        width: '100%',
        transform: 'translateY(100%)',
      }),
    ], { optional: true }),
    
    query(':leave', [
      animate('300ms ease-in', style({ transform: 'translateY(-100%)' })),
    ], { optional: true }),
    
    query(':enter', [
      animate('300ms ease-out', style({ transform: 'translateY(0%)' })),
    ], { optional: true })
  ]),
]);
